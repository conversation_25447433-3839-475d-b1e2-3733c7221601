-- 百家乐测试数据插入脚本
-- 这些数据用于验证计算逻辑的正确性

-- 清理现有测试数据
DELETE FROM bet_records WHERE wash_code LIKE 'TEST%';

-- 测试用例1：庄赢（客户投注庄）
-- 预期：客户赢950，输口0，洗码量0
INSERT INTO bet_records (
    table_id, tables_name, game_type, account_period, round_no, hand_no,
    wash_code, user_name, currency_type,
    banker_amount, player_amount, tie_amount, banker_pair_amount, player_pair_amount,
    lucky_6_amount, lucky_7_amount, amount_bottom,
    win_result, win_loss, loss, amount_tip, wash_rate, wash_amount, wash_tip,
    create_time
) VALUES (
    1, '测试桌台1', 1, '********', 1, 1,
    'TEST001', '测试用户1', 1,
    1000.00, 0.00, 0.00, 0.00, 0.00,
    0.00, 0.00, 0.00,
    '庄', 950.00, 0.00, 0.00, 0.0100, 0.00, 0.00,
    NOW()
);

-- 测试用例2：闲赢（客户投注闲）
-- 预期：客户赢1000，输口0，洗码量0
INSERT INTO bet_records (
    table_id, tables_name, game_type, account_period, round_no, hand_no,
    wash_code, user_name, currency_type,
    banker_amount, player_amount, tie_amount, banker_pair_amount, player_pair_amount,
    lucky_6_amount, lucky_7_amount, amount_bottom,
    win_result, win_loss, loss, amount_tip, wash_rate, wash_amount, wash_tip,
    create_time
) VALUES (
    1, '测试桌台1', 1, '********', 1, 2,
    'TEST002', '测试用户2', 1,
    0.00, 1000.00, 0.00, 0.00, 0.00,
    0.00, 0.00, 0.00,
    '闲', 1000.00, 0.00, 0.00, 0.0100, 0.00, 0.00,
    NOW()
);

-- 测试用例3：庄赢（客户投注闲）
-- 预期：客户输1000，输口1000，洗码量1000
INSERT INTO bet_records (
    table_id, tables_name, game_type, account_period, round_no, hand_no,
    wash_code, user_name, currency_type,
    banker_amount, player_amount, tie_amount, banker_pair_amount, player_pair_amount,
    lucky_6_amount, lucky_7_amount, amount_bottom,
    win_result, win_loss, loss, amount_tip, wash_rate, wash_amount, wash_tip,
    create_time
) VALUES (
    1, '测试桌台1', 1, '********', 1, 3,
    'TEST003', '测试用户3', 1,
    0.00, 1000.00, 0.00, 0.00, 0.00,
    0.00, 0.00, 0.00,
    '庄', -1000.00, 1000.00, 0.00, 0.0100, 1000.00, 10.00,
    NOW()
);

-- 测试用例4：和赢（和的投注本金变小费）
-- 预期：客户赢8000，输口0，洗码量0，小费1000
INSERT INTO bet_records (
    table_id, tables_name, game_type, account_period, round_no, hand_no,
    wash_code, user_name, currency_type,
    banker_amount, player_amount, tie_amount, banker_pair_amount, player_pair_amount,
    lucky_6_amount, lucky_7_amount, amount_bottom,
    win_result, win_loss, loss, amount_tip, wash_rate, wash_amount, wash_tip,
    create_time
) VALUES (
    1, '测试桌台1', 1, '********', 1, 4,
    'TEST004', '测试用户4', 1,
    0.00, 0.00, 1000.00, 0.00, 0.00,
    0.00, 0.00, 0.00,
    '和', 8000.00, 0.00, 1000.00, 0.0100, 0.00, 0.00,
    NOW()
);

-- 测试用例5：庄对赢（和底变小费）
-- 预期：客户赢2050，输口0，洗码量0，小费50
INSERT INTO bet_records (
    table_id, tables_name, game_type, account_period, round_no, hand_no,
    wash_code, user_name, currency_type,
    banker_amount, player_amount, tie_amount, banker_pair_amount, player_pair_amount,
    lucky_6_amount, lucky_7_amount, amount_bottom,
    win_result, win_loss, loss, amount_tip, wash_rate, wash_amount, wash_tip,
    create_time
) VALUES (
    1, '测试桌台1', 1, '********', 1, 5,
    'TEST005', '测试用户5', 1,
    1000.00, 0.00, 0.00, 100.00, 0.00,
    0.00, 0.00, 50.00,
    '庄,庄对', 2050.00, 0.00, 50.00, 0.0100, 0.00, 0.00,
    NOW()
);

-- 测试用例6：复合投注（客户输钱）
-- 预期：客户输725，输口725，洗码量725
INSERT INTO bet_records (
    table_id, tables_name, game_type, account_period, round_no, hand_no,
    wash_code, user_name, currency_type,
    banker_amount, player_amount, tie_amount, banker_pair_amount, player_pair_amount,
    lucky_6_amount, lucky_7_amount, amount_bottom,
    win_result, win_loss, loss, amount_tip, wash_rate, wash_amount, wash_tip,
    create_time
) VALUES (
    1, '测试桌台1', 1, '********', 1, 6,
    'TEST006', '测试用户6', 1,
    500.00, 800.00, 200.00, 100.00, 100.00,
    0.00, 0.00, 30.00,
    '庄', -725.00, 725.00, 0.00, 0.0100, 725.00, 7.25,
    NOW()
);

-- 测试用例7：闲对赢（和底变小费）
-- 预期：客户赢1150，输口0，洗码量0，小费30
INSERT INTO bet_records (
    table_id, tables_name, game_type, account_period, round_no, hand_no,
    wash_code, user_name, currency_type,
    banker_amount, player_amount, tie_amount, banker_pair_amount, player_pair_amount,
    lucky_6_amount, lucky_7_amount, amount_bottom,
    win_result, win_loss, loss, amount_tip, wash_rate, wash_amount, wash_tip,
    create_time
) VALUES (
    1, '测试桌台1', 1, '********', 1, 7,
    'TEST007', '测试用户7', 1,
    0.00, 1000.00, 0.00, 0.00, 100.00,
    0.00, 0.00, 30.00,
    '闲,闲对', 2230.00, 0.00, 30.00, 0.0100, 0.00, 0.00,
    NOW()
);

-- 测试用例8：全输（所有投注都没中）
-- 预期：客户输1730，输口1730，洗码量1730
INSERT INTO bet_records (
    table_id, tables_name, game_type, account_period, round_no, hand_no,
    wash_code, user_name, currency_type,
    banker_amount, player_amount, tie_amount, banker_pair_amount, player_pair_amount,
    lucky_6_amount, lucky_7_amount, amount_bottom,
    win_result, win_loss, loss, amount_tip, wash_rate, wash_amount, wash_tip,
    create_time
) VALUES (
    1, '测试桌台1', 1, '********', 1, 8,
    'TEST008', '测试用户8', 1,
    500.00, 800.00, 200.00, 100.00, 100.00,
    0.00, 0.00, 30.00,
    '和', -1530.00, 1530.00, 200.00, 0.0100, 1530.00, 15.30,
    NOW()
);

-- 查询测试数据
SELECT 
    hand_no,
    wash_code,
    user_name,
    banker_amount,
    player_amount,
    tie_amount,
    banker_pair_amount,
    player_pair_amount,
    amount_bottom,
    win_result,
    win_loss,
    loss,
    amount_tip,
    wash_amount,
    wash_tip
FROM bet_records 
WHERE wash_code LIKE 'TEST%' 
ORDER BY hand_no;

-- 验证计算结果的SQL查询
SELECT 
    '测试用例' as type,
    hand_no,
    wash_code,
    CONCAT('庄:', banker_amount, ' 闲:', player_amount, ' 和:', tie_amount, ' 庄对:', banker_pair_amount, ' 闲对:', player_pair_amount, ' 和底:', amount_bottom) as bet_amounts,
    win_result,
    win_loss,
    loss,
    wash_amount,
    amount_tip,
    CASE 
        WHEN win_loss > 0 THEN '客户赢'
        WHEN win_loss < 0 THEN '客户输'
        ELSE '平局'
    END as result_type
FROM bet_records 
WHERE wash_code LIKE 'TEST%' 
ORDER BY hand_no;
