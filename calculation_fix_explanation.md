# 百家乐计算逻辑修复说明

## 问题分析

原来的计算逻辑存在以下问题，导致数据库中输赢结果都是0：

### 1. 主要问题
- **闲赢计算错误**：只加了本金，没有加赔付
- **庄赢赔率理解错误**：直接乘以1.95，但应该理解为净赔率
- **输赢计算逻辑混乱**：没有正确计算客户的净输赢

### 2. 修复方案

#### 新的计算逻辑：
1. **总投注金额** = 所有投注项的金额总和
2. **总赢得金额** = 中奖投注项的（本金 + 赔付）
3. **净输赢** = 总赢得金额 - 总投注金额
4. **输口** = 如果净输赢 < 0，则输口 = -净输赢；否则输口 = 0
5. **洗码量** = 输口（业务规则：洗码量都是单边戏码，输口=洗码量）
6. **洗码费** = 洗码量 × 洗码率

#### 赔率说明：
- **庄**：0.95（净赔率，扣除5%佣金）
- **闲**：1.0（净赔率，1:1）
- **和**：8.0（净赔率，1:8）
- **庄对/闲对**：11.0（净赔率，1:11）
- **幸运6**：12.0（净赔率，1:12）
- **幸运7**：50.0（净赔率，1:50）

## 测试用例

### 测试用例1：庄赢
- **投注**：庄1000，闲0，其他0
- **结果**：["庄"]
- **计算**：
  - 总投注：1000
  - 总赢得：1000 + (1000 × 0.95) = 1950
  - 净输赢：1950 - 1000 = 950
  - 输口：0（客户赢了）
  - 洗码量：0（输口=洗码量）

### 测试用例2：闲赢
- **投注**：庄0，闲1000，其他0
- **结果**：["闲"]
- **计算**：
  - 总投注：1000
  - 总赢得：1000 + (1000 × 1.0) = 2000
  - 净输赢：2000 - 1000 = 1000
  - 输口：0（客户赢了）
  - 洗码量：0（输口=洗码量）

### 测试用例3：庄赢但客户投注闲
- **投注**：庄0，闲1000，其他0
- **结果**：["庄"]
- **计算**：
  - 总投注：1000
  - 总赢得：0（没有中奖投注）
  - 净输赢：0 - 1000 = -1000
  - 输口：1000（客户输了1000）
  - 洗码量：1000（输口=洗码量）

### 测试用例4：复合投注
- **投注**：庄1000，闲500，庄对100
- **结果**：["庄", "庄对"]
- **计算**：
  - 总投注：1600
  - 总赢得：(1000 + 1000×0.95) + (100 + 100×11) = 1950 + 1200 = 3150
  - 净输赢：3150 - 1600 = 1550
  - 输口：0（客户赢了）
  - 洗码量：0（输口=洗码量）

### 测试用例5：小费计算（和结果）
- **投注**：庄0，闲0，和1000，和底50
- **结果**：["和"]
- **计算**：
  - 总投注：1000
  - 总赢得：1000 + (1000×8) = 9000
  - 净输赢：9000 - 1000 = 8000
  - 输口：0（客户赢了）
  - 洗码量：0（输口=洗码量）
  - 小费：1000（和的投注本金变为小费）

### 测试用例6：小费计算（庄对结果）
- **投注**：庄1000，庄对100，和底50
- **结果**：["庄", "庄对"]
- **计算**：
  - 总投注：1100
  - 总赢得：(1000 + 1000×0.95) + (100 + 100×11) = 1950 + 1200 = 3150
  - 净输赢：3150 - 1100 = 2050
  - 输口：0（客户赢了）
  - 洗码量：0（输口=洗码量）
  - 小费：50（和底本金变为小费）

## 使用方法

1. **更新数据库赔率**：
   ```sql
   -- 执行 update_odds.sql
   UPDATE tables_bets SET odds = 0.9500 WHERE bet_area = '庄';
   UPDATE tables_bets SET odds = 1.0000 WHERE bet_area = '闲';
   ```

2. **重启服务**：
   ```bash
   # 重启Go服务以加载新的计算逻辑
   ```

3. **测试**：
   - 使用 `test_result_calculation.html` 进行测试
   - 先发送下注记录
   - 再录入结果进行结算
   - 检查数据库中的输赢结果是否正确

## 注意事项

1. **赔率配置**：确保数据库中的赔率是净赔率（不包含本金）
2. **洗码量计算**：按业务规则，洗码量=输口（单边戏码）
3. **小费处理**：
   - 和结果：和的投注本金变为小费
   - 庄对结果：和底（通用底注）本金变为小费
   - 闲对结果：和底（通用底注）本金变为小费
   - 百家乐不需要和底统计
4. **日志记录**：新增了详细的计算日志，便于调试

## 验证方法

可以通过以下方式验证修复是否成功：

1. 查看服务日志中的计算结果
2. 检查数据库bet_records表中的win_loss字段
3. 使用测试页面进行端到端测试
