# Settlement Records 结构更新说明

## 更新概述

修改 `settlement_records` 表结构，将 `compare_result` 字段类型从 `varchar(255)` 改为 `decimal(12,6)`，并新增 `compare_type` 字段。

## 字段变更详情

### 1. compare_result 字段修改
- **原类型**: `varchar(255)` - 文本描述
- **新类型**: `decimal(12,6)` - 数值型
- **说明**: 存储点码与注单比对的数值差异

### 2. compare_type 字段新增
- **类型**: `tinyint(1)`
- **说明**: 比较类型枚举
- **取值**:
  - `1` - 多收（点码多于注单）
  - `2` - 多出（点码少于注单）
  - `3` - 正确（点码与注单一致）
  - `0` - 暂无数据

## 数据示例

### 修改前（文本格式）
```
compare_result: "点码与注单一致"
compare_result: "点码多于注单 +25000"
compare_result: "点码少于注单 -15000"
```

### 修改后（数值格式）
```
compare_result: 0.00,      compare_type: 3  // 正确
compare_result: 25000.00,  compare_type: 1  // 多收
compare_result: -15000.00, compare_type: 2  // 多出
```

## 影响的文件

### 1. 数据库模型
- `internal/models/settlement_records.go` - 更新字段类型和新增字段

### 2. 服务层
- `internal/services/finance_service.go` - 更新请求/响应结构和处理逻辑
- `internal/services/table_service.go` - 更新收盘展示相关结构

### 3. WebSocket处理
- `internal/websocket/client.go` - 更新字段解析逻辑

### 4. 数据库脚本
- `sql` - 更新表结构定义
- `migration_settlement_records_update.sql` - 数据库迁移脚本
- `insert_settlement_test_data.sql` - 更新测试数据

### 5. 文档
- `docs/websocket_api.md` - 更新API文档
- `SETTLEMENT_DISPLAY_API.md` - 更新收盘展示API文档

## 数据库迁移

### 执行迁移脚本
```sql
-- 执行迁移
source migration_settlement_records_update.sql;

-- 插入测试数据
source insert_settlement_test_data.sql;
```

### 验证迁移结果
```sql
-- 检查表结构
DESCRIBE settlement_records;

-- 查看字段详情
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'settlement_records' 
  AND COLUMN_NAME IN ('compare_result', 'compare_type')
ORDER BY ORDINAL_POSITION;
```

## API 变更

### 批量点码接口 (batch_settlement)

#### 请求格式变更
```json
{
  "compare_result": 25000.00,    // 原为字符串，现为数值
  "compare_type": 1,             // 新增字段
  "remark": "备注信息"
}
```

#### 响应格式变更
```json
{
  "compare_result": 25000.00,    // 原为字符串，现为数值
  "compare_type": 1,             // 新增字段
  "remark": "备注信息"
}
```

### 收盘展示接口 (get_settlement_display)

#### 响应格式变更
```json
{
  "currency_type": 1,
  "total_amount": 670000.00,
  "compare_result": 0.00,        // 原为字符串，现为数值
  "compare_type": 3              // 新增字段
}
```

## 向后兼容性

### 注意事项
1. **数据类型不兼容**: `varchar` 到 `decimal` 的转换会清空现有数据
2. **API 变更**: 客户端需要更新以处理新的数据格式
3. **测试数据**: 需要重新插入符合新格式的测试数据

### 建议
1. 在生产环境执行前，先备份现有数据
2. 通知前端开发人员更新相关代码
3. 更新API文档和测试用例

## 测试验证

### 1. 数据库结构验证
- 确认字段类型正确修改
- 确认新字段成功添加

### 2. API 功能验证
- 测试批量点码接口
- 测试收盘展示接口
- 验证数据格式正确

### 3. 业务逻辑验证
- 验证比较类型枚举值正确
- 验证数值计算逻辑正确
