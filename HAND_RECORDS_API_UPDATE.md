# 获取条口记录结果接口调整说明

## 调整概述

将获取条口记录结果接口从"获取桌台账期全部结果"调整为"获取每一场的结果"，以支持更精确的露珠图生成。

## 接口变更详情

### 1. 请求参数变更

#### 修改前
```json
{
  "type": "get_hand_records_result",
  "data": {
    "account_period": "********"
  }
}
```

#### 修改后
```json
{
  "type": "get_hand_records_result",
  "data": {
    "account_period": "********",
    "shoe_no": 1
  }
}
```

**新增参数：**
- `shoe_no`: 场次编号（必需，整数，大于0）

### 2. 响应数据变更

#### 修改前
```json
{
  "message": "查询成功",
  "table_id": 1,
  "table_code": "A01",
  "records": [...],
  "total_count": 10
}
```

#### 修改后
```json
{
  "message": "查询成功，找到场次1的2条记录",
  "table_id": 1,
  "table_code": "A01",
  "account_period": "********",
  "shoe_no": 1,
  "records": [...],
  "total_count": 2
}
```

**新增字段：**
- `account_period`: 账期
- `shoe_no`: 场次编号

### 3. 查询逻辑变更

#### 修改前
```sql
WHERE table_id = ? AND account_period = ?
ORDER BY shoe_no ASC, hand_no ASC
```

#### 修改后
```sql
WHERE table_id = ? AND account_period = ? AND shoe_no = ?
ORDER BY hand_no ASC
```

**变更说明：**
- 增加了 `shoe_no` 过滤条件
- 移除了 `shoe_no` 排序（因为只查询单个场次）
- 保留了 `hand_no` 排序（按局数升序）

## 影响的文件

### 1. 服务层
- `internal/services/table_service.go`
  - 修改 `GetHandRecordsResultRequest` 结构体
  - 修改 `GetHandRecordsResultResponse` 结构体
  - 修改 `GetHandRecordsResult` 方法实现

### 2. WebSocket处理
- `internal/websocket/client.go`
  - 修改请求参数解析逻辑
  - 修改响应数据构造逻辑

### 3. 文档
- `docs/websocket_api.md` - 更新API文档

### 4. 测试页面
- `static/test_hand_records_result.html` - 新增专用测试页面
- `static/api_list.html` - 添加测试页面链接

## 业务逻辑变更

### 1. 参数验证
- 新增场次编号验证（必须大于0）
- 保留账期格式验证

### 2. 数据查询
- 从查询整个账期改为查询指定场次
- 减少数据量，提高查询效率
- 更精确的数据范围

### 3. 响应消息
- 消息内容更具体，包含场次信息
- 添加查询到的记录数量信息

## 使用场景

### 1. 露珠图生成
- 前端可以按场次请求数据
- 支持分场次显示露珠图
- 减少不必要的数据传输

### 2. 实时更新
- 当前场次结束时，可以立即获取该场次的完整数据
- 支持历史场次的回顾查看

### 3. 性能优化
- 减少单次查询的数据量
- 提高响应速度
- 降低网络传输成本

## 向后兼容性

### 注意事项
1. **参数变更**: 客户端必须提供 `shoe_no` 参数
2. **响应格式**: 新增了 `account_period` 和 `shoe_no` 字段
3. **数据范围**: 返回的数据范围从整个账期缩小到单个场次

### 迁移建议
1. 更新客户端代码，添加场次编号参数
2. 修改前端逻辑，支持按场次获取和显示数据
3. 测试新接口的功能和性能

## 测试验证

### 1. 功能测试
- 使用测试页面验证接口功能
- 测试不同场次的数据查询
- 验证参数验证逻辑

### 2. 数据验证
- 确认返回的数据只包含指定场次
- 验证数据排序正确（按局数升序）
- 检查响应字段完整性

### 3. 错误处理
- 测试无效场次编号的处理
- 测试不存在数据的场景
- 验证错误消息的准确性

## 示例用法

### JavaScript 客户端示例
```javascript
// 获取第1场的条口记录
const message = {
    type: 'get_hand_records_result',
    data: {
        account_period: '********',
        shoe_no: 1
    }
};

ws.send(JSON.stringify(message));
```

### 响应处理示例
```javascript
ws.onmessage = function(event) {
    const response = JSON.parse(event.data);
    
    if (response.type === 'get_hand_records_result_success') {
        const data = response.data;
        console.log(`场次 ${data.shoe_no} 共有 ${data.total_count} 条记录`);
        
        // 生成露珠图
        generatePearlRoad(data.records);
    }
};
```

## 总结

此次调整使获取条口记录结果接口更加精确和高效，支持按场次获取数据，更好地满足露珠图生成的需求。同时提供了完整的测试页面和文档，便于开发和测试。
