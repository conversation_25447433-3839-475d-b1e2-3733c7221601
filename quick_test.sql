-- 快速测试脚本 - 一键验证百家乐计算逻辑
-- 使用方法: mysql -u用户名 -p密码 数据库名 < quick_test.sql

-- 1. 更新赔率配置
UPDATE tables_bets SET odds = 0.9500 WHERE bet_area = '庄';
UPDATE tables_bets SET odds = 1.0000 WHERE bet_area = '闲';

-- 2. 清理旧的测试数据
DELETE FROM bet_records WHERE wash_code LIKE 'TEST%';

-- 3. 显示当前赔率配置
SELECT '=== 当前赔率配置 ===' as info;
SELECT bet_area, odds FROM tables_bets ORDER BY bet_area;

-- 4. 手动计算验证（模拟计算逻辑）
SELECT '=== 手动计算验证 ===' as info;

-- 测试用例1：庄赢，客户投注庄1000
SELECT 
    '测试用例1' as test_case,
    '庄赢-客户投注庄' as description,
    1000 as total_bet,
    1000 + (1000 * 0.95) as total_win,
    (1000 + (1000 * 0.95)) - 1000 as net_win_loss,
    0 as expected_loss,
    0 as expected_wash_amount,
    0 as expected_tip;

-- 测试用例2：闲赢，客户投注闲1000
SELECT 
    '测试用例2' as test_case,
    '闲赢-客户投注闲' as description,
    1000 as total_bet,
    1000 + (1000 * 1.0) as total_win,
    (1000 + (1000 * 1.0)) - 1000 as net_win_loss,
    0 as expected_loss,
    0 as expected_wash_amount,
    0 as expected_tip;

-- 测试用例3：庄赢，客户投注闲1000
SELECT 
    '测试用例3' as test_case,
    '庄赢-客户投注闲' as description,
    1000 as total_bet,
    0 as total_win,
    0 - 1000 as net_win_loss,
    1000 as expected_loss,
    1000 as expected_wash_amount,
    0 as expected_tip;

-- 测试用例4：和赢，客户投注和1000
SELECT 
    '测试用例4' as test_case,
    '和赢-和投注本金变小费' as description,
    1000 as total_bet,
    1000 + (1000 * 8.0) as total_win,
    (1000 + (1000 * 8.0)) - 1000 as net_win_loss,
    0 as expected_loss,
    0 as expected_wash_amount,
    1000 as expected_tip;

-- 测试用例5：庄对赢，庄1000+庄对100+和底50
SELECT 
    '测试用例5' as test_case,
    '庄对赢-和底变小费' as description,
    1100 as total_bet,
    (1000 + 1000*0.95) + (100 + 100*11) as total_win,
    ((1000 + 1000*0.95) + (100 + 100*11)) - 1100 as net_win_loss,
    0 as expected_loss,
    0 as expected_wash_amount,
    50 as expected_tip;

-- 5. 检查tables表的洗码率配置
SELECT '=== 桌台洗码率配置 ===' as info;
SELECT id, tables_name, wash_rate FROM tables WHERE id = 1;

-- 6. 如果没有桌台记录，插入一个测试桌台
INSERT IGNORE INTO tables (id, tables_name, game_type, status, wash_rate, create_time) 
VALUES (1, '测试桌台1', 1, 2, 0.01, NOW());

-- 7. 检查是否有tables_bets配置，如果没有则插入
INSERT IGNORE INTO tables_bets (table_id, bet_area, odds) VALUES
(1, '庄', 0.9500),
(1, '闲', 1.0000),
(1, '和', 8.0000),
(1, '庄对', 11.0000),
(1, '闲对', 11.0000),
(1, '幸运6', 12.0000),
(1, '幸运7', 50.0000);

-- 8. 显示配置完成信息
SELECT '=== 配置完成 ===' as info;
SELECT 
    '数据库配置已完成，可以开始测试' as message,
    '请使用 generate_test_data.html 进行WebSocket测试' as next_step,
    '或者运行 insert_test_data.sql 插入预设测试数据' as alternative;

-- 9. 显示测试命令
SELECT '=== 测试命令 ===' as info;
SELECT 
    'WebSocket测试' as test_type,
    '打开 generate_test_data.html' as command,
    '连接 ws://localhost:8080/ws' as url;

SELECT 
    '数据库测试' as test_type,
    'source insert_test_data.sql' as command,
    '然后运行 source verify_test_results.sql' as verification;
