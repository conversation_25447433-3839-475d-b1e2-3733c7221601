# 记账系统后端服务

基于Go语言开发的记账系统后端服务，使用WebSocket进行实时通信。

## 功能特性

### 用户认证系统
- ✅ 用户登录/登出
- ✅ JWT令牌认证
- ✅ 密码加密存储（MD5+盐值）
- ✅ 用户信息管理
- ✅ 密码修改功能
- ✅ 令牌验证
- ✅ 用户状态管理
- ✅ 登录次数统计和IP记录
- ✅ 完整的登录日志记录

### 桌台管理系统
- ✅ 根据IP获取桌台信息
- ✅ 桌台下注配置管理
- ✅ 实时桌台状态查询

### WebSocket通信
- ✅ 实时双向通信
- ✅ 消息类型路由
- ✅ 客户端连接管理
- ✅ 认证状态管理

### 客户信息管理
- ✅ 通过洗码号精确查询客户信息
- ✅ 账号状态验证（仅限正常状态）
- ✅ 完整的客户资料展示
- ✅ 支持主号和分线账号管理
- ✅ 返点比例和客户类型管理

## 技术栈

- **语言**: Go 1.21
- **数据库**: MySQL + Redis
- **通信协议**: WebSocket
- **认证**: JWT
- **ORM**: GORM
- **配置管理**: Viper

## 快速开始

### 1. 环境准备

确保你的系统已安装：
- Go 1.21+
- MySQL 5.7+
- Redis 6.0+

### 2. 配置数据库

修改 `config/config.yaml` 文件中的数据库配置：

```yaml
mysql:
  host: localhost
  port: 3306
  username: root
  password: your_password
  database: accounting_enter
  charset: utf8mb4

redis:
  host: localhost
  port: 6379
  password: ""
  database: 0
```

### 3. 初始化数据库

确保数据库中已创建所有必要的表结构（见数据库DDL文档）。

### 4. 创建初始用户

```bash
# 编译并运行用户初始化脚本
go run scripts/init_user.go
```

这将创建以下测试用户：
- 管理员: admin / 123456
- 测试用户: test / 123456

### 5. 启动服务

```bash
# 编译项目
go build -o accounting_enter.exe .

# 运行服务
./accounting_enter.exe
```

服务将在 `http://localhost:8080` 启动。

### 6. 测试接口

访问 `http://localhost:8080/static/test_table_api.html` 打开测试页面，可以测试所有WebSocket接口。

## API接口文档

详细的API文档请查看：[WebSocket API 文档](docs/websocket_api.md)

### 主要接口

#### 用户认证接口

| 接口 | 消息类型 | 说明 |
|------|----------|------|
| 用户登录 | `login` | 用户账号密码登录 |
| 用户登出 | `logout` | 用户登出并清除会话 |
| 获取用户信息 | `get_user_info` | 查询当前用户详细信息 |
| 修改密码 | `change_password` | 用户修改密码 |
| 验证令牌 | `verify_token` | 验证JWT令牌有效性 |


#### 桌台信息接口

| 接口 | 消息类型 | 说明 |
|------|----------|------|
| 获取桌台信息 | `get_table_info` | 根据客户端IP自动获取桌台信息 |

#### 客户信息接口

| 接口 | 消息类型 | 说明 |
|------|----------|------|
| 通过洗码号获取客户信息 | `get_user_by_wash_code` | 根据洗码号查询客户详细信息（仅限正常状态客户） |

#### 出码申请接口

| 接口 | 消息类型 | 说明 |
|------|----------|------|
| 申请出码 | `apply_out_code` | 提交出码申请，操作人为当前登录用户 |
| 获取出码申请列表 | `get_out_code_list` | 获取出码申请列表，支持过滤和分页 |
| 获取出码申请详情 | `get_out_code_detail` | 根据申请ID获取详细信息 |

## 项目结构

```
accounting_enter/
├── config/                 # 配置文件
│   └── config.yaml
├── docs/                   # 文档
│   └── websocket_api.md
├── internal/               # 内部包
│   ├── config/            # 配置管理
│   ├── database/          # 数据库连接
│   ├── handlers/          # HTTP处理器
│   ├── models/            # 数据模型
│   ├── routes/            # 路由配置
│   ├── services/          # 业务逻辑
│   └── websocket/         # WebSocket处理
├── scripts/               # 脚本文件
│   └── init_user.go      # 用户初始化脚本
├── static/                # 静态文件
│   ├── index.html
│   └── test_table_api.html
├── go.mod
├── go.sum
├── main.go
├── Makefile
└── README.md
```

## 开发规范

### 代码规范
- 遵循Go语言官方编码规范
- 使用有意义的变量和函数命名
- 添加必要的注释和文档
- 错误处理要完整

### 数据库规范
- 使用GORM进行数据库操作
- 所有表都包含软删除标记（mark字段）
- 时间字段使用Unix时间戳
- 字段命名使用下划线分隔

### WebSocket消息格式
```json
{
  "type": "消息类型",
  "from": "发送者ID",
  "to": "接收者ID（可选）",
  "data": "消息数据",
  "timestamp": 1234567890
}
```

## 安全特性

### 认证安全
- 密码使用MD5+随机盐值加密
- JWT令牌有效期24小时
- 自动检测令牌过期
- 支持主动登出清除认证状态

### 数据安全
- 软删除保护重要数据
- 输入参数验证
- SQL注入防护（GORM）
- XSS防护建议

## 测试

### 功能测试
访问测试页面进行完整的功能测试：
```
http://localhost:8080/static/test_table_api.html
```

### 单元测试
```bash
# 运行所有测试
go test ./...

# 运行特定包的测试
go test ./internal/services
```

### 测试说明

1. **初始化用户数据**
   ```bash
   go run scripts/init_user.go
   ```

2. **测试认证功能**
   - 使用默认账号 `admin/123456` 登录
   - 测试获取用户信息
   - 测试修改密码
   - 测试令牌验证

   - 测试用户登出

3. **测试桌台信息**
   - 点击"获取桌台信息"按钮
   - 查看桌台配置和下注信息

4. **测试客户信息**
   - 在洗码号输入框输入有效洗码号
   - 点击"获取客户信息"按钮
   - 查看客户详细信息（仅显示正常状态客户）

## 部署

### 生产环境配置
1. 修改JWT密钥为强密码
2. 使用HTTPS/WSS协议
3. 配置防火墙规则
4. 设置日志轮转
5. 配置监控告警

### Docker部署
```bash
# 构建镜像
docker build -t accounting-enter .

# 运行容器
docker run -d -p 8080:8080 accounting-enter
```

## 常见问题

### Q: WebSocket连接失败？
A: 检查防火墙设置，确保8080端口开放。

### Q: 数据库连接失败？
A: 检查配置文件中的数据库连接信息是否正确。

### Q: 登录提示用户名或密码错误？
A: 确保已运行用户初始化脚本创建测试用户。

### Q: 令牌验证失败？
A: JWT令牌有效期为24小时，过期后需要重新登录。

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 更新日志

### v1.1.0 (当前版本)
- ✅ 新增用户认证系统
- ✅ 实现JWT令牌认证
- ✅ 添加密码加密存储
- ✅ 支持用户信息管理
- ✅ 完善WebSocket接口文档
- ✅ 添加可视化测试工具

### v1.0.0
- ✅ 基础WebSocket通信框架
- ✅ 桌台信息查询功能
- ✅ 数据库模型定义
- ✅ 基础项目结构 