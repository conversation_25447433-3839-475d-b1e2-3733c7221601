# WebSocket连接中断故障排除指南

## 问题现象

在批量下注录入测试时，WebSocket连接经常出现中断。

## 可能原因分析

### 1. 消息大小限制
- **问题**：批量数据超过WebSocket消息大小限制
- **解决方案**：已增加消息大小限制到1MB

### 2. 超时设置不合理
- **问题**：读写超时时间过短，批量操作容易超时
- **解决方案**：已调整超时设置

### 3. Redis连接问题
- **问题**：Redis连接超时或连接池耗尽
- **解决方案**：已优化Redis连接配置

### 4. 错误处理不完善
- **问题**：单条记录错误导致整个连接中断
- **解决方案**：已添加panic恢复机制

## 测试工具

### 1. 快速批量下注测试
访问：`http://localhost:8080/quick_bet_test.html`

**特点**：
- 简化的测试界面
- 可调节记录数量（1-20条）
- 连续测试功能
- 实时统计信息

### 2. WebSocket稳定性测试
访问：`http://localhost:8080/test_websocket_stability.html`

**特点**：
- 完整的测试功能
- 详细的日志记录
- 连接状态监控
- 性能统计

## 故障排除步骤

### 步骤1：检查连接状态
1. 打开快速测试页面
2. 点击"连接"按钮
3. 观察连接状态是否正常

### 步骤2：测试登录
1. 连接成功后点击"登录"按钮
2. 确认登录状态

### 步骤3：单次测试
1. 设置较小的记录数量（如5条）
2. 点击"单次测试"
3. 观察是否成功

### 步骤4：连续测试
1. 设置适中的记录数量（如10条）
2. 点击"开始连续测试"
3. 观察连接稳定性

## 日志分析

### 服务端日志
查看控制台输出的详细日志：
- 客户端连接/断开日志
- 消息大小监控
- Redis连接池状态
- 批量处理耗时

### 客户端日志
在测试页面观察：
- 连接状态变化
- 消息发送/接收
- 错误信息

## 常见问题解决

### 问题1：连接立即断开
**可能原因**：
- 服务未启动
- 端口被占用
- 防火墙阻止

**解决方法**：
1. 确认服务已启动
2. 检查端口8080是否可用
3. 检查防火墙设置

### 问题2：登录失败
**可能原因**：
- 用户名密码错误
- 数据库连接问题

**解决方法**：
1. 确认用户名：admin，密码：123456
2. 检查数据库连接

### 问题3：批量下注失败
**可能原因**：
- 记录数量过多
- 数据格式错误
- Redis连接问题

**解决方法**：
1. 减少记录数量
2. 检查数据格式
3. 确认Redis服务正常

### 问题4：连接频繁断开
**可能原因**：
- 网络不稳定
- 服务端资源不足
- 客户端发送过快

**解决方法**：
1. 检查网络连接
2. 增加服务端资源
3. 降低发送频率

## 性能优化建议

### 1. 调整记录数量
- 单次测试：5-10条记录
- 连续测试：3-5条记录
- 避免一次性发送过多数据

### 2. 网络环境
- 确保网络稳定
- 避免在网络高峰期测试
- 使用有线连接而非WiFi

### 3. 系统资源
- 确保有足够的内存
- 监控CPU使用率
- 检查磁盘空间

## 监控指标

### 关键指标
1. **连接中断次数**：应该为0或很少
2. **成功率**：应该接近100%
3. **响应时间**：应该在合理范围内
4. **错误率**：应该很低

### 正常值参考
- 连接中断：< 5次/小时
- 成功率：> 95%
- 响应时间：< 5秒
- 错误率：< 5%

## 联系支持

如果问题仍然存在，请提供以下信息：
1. 测试页面截图
2. 服务端日志
3. 客户端错误信息
4. 系统环境信息

## 更新日志

### v1.1 (最新)
- 增加详细的日志记录
- 优化Redis连接配置
- 添加连接池监控
- 改进错误处理机制
- 创建快速测试工具

### v1.0
- 基础WebSocket功能
- 批量下注录入
- 基本错误处理 