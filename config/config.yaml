server:
  host: 0.0.0.0
  port: 8080
  mode: debug

database:
  mysql:
    host: **************
    port: 3306
    username: root
    password: Root123456@
    database: accounting
    charset: utf8mb4
    max_idle_conns: 10
    max_open_conns: 100
    max_lifetime: 300

redis:
  host: localhost
  port: 6379
  password: ""
  database: 0
  pool_size: 10
  max_retries: 3

websocket:
  read_buffer_size: 1024
  write_buffer_size: 1024
  check_origin: true

log:
  level: info
  format: json
  output: logs/app.log 