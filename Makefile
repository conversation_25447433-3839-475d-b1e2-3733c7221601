.PHONY: build run clean test swagger deps

# 项目名称
PROJECT_NAME = accounting_enter

# Go相关变量
GOCMD = go
GOBUILD = $(GOCMD) build
GOCLEAN = $(GOCMD) clean
GOTEST = $(GOCMD) test
GOGET = $(GOCMD) get
GOMOD = $(GOCMD) mod
BINARY_NAME = $(PROJECT_NAME)
BINARY_UNIX = $(BINARY_NAME)_unix

# 构建项目
build:
	@echo "构建项目..."
	$(GOBUILD) -o $(BINARY_NAME) -v ./main.go

# 运行项目
run:
	@echo "运行项目..."
	$(GOCMD) run main.go

# 清理构建文件
clean:
	@echo "清理构建文件..."
	$(GOCLEAN)
	rm -f $(BINARY_NAME)
	rm -f $(BINARY_UNIX)

# 运行测试
test:
	@echo "运行测试..."
	$(GOTEST) -v ./...

# 生成Swagger文档
swagger:
	@echo "生成Swagger文档..."
	swag init -g main.go -o ./docs

# 安装依赖
deps:
	@echo "安装项目依赖..."
	$(GOMOD) download
	$(GOMOD) tidy

# 交叉编译Linux版本
build-linux:
	@echo "构建Linux版本..."
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 $(GOBUILD) -o $(BINARY_UNIX) -v ./main.go

# 安装工具
install-tools:
	@echo "安装开发工具..."
	$(GOGET) -u github.com/swaggo/swag/cmd/swag

# 格式化代码
fmt:
	@echo "格式化代码..."
	$(GOCMD) fmt ./...

# 检查代码
vet:
	@echo "检查代码..."
	$(GOCMD) vet ./...

# 完整构建流程
all: clean deps swagger build

# 开发环境启动
dev: deps swagger run

# Docker相关
docker-build:
	@echo "构建Docker镜像..."
	docker build -t $(PROJECT_NAME):latest .

docker-run:
	@echo "运行Docker容器..."
	docker run -p 8080:8080 $(PROJECT_NAME):latest 