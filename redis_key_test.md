# Redis Key 一致性修复验证

## 问题描述
下注接口保存的Redis key与结果录入接口查询使用的key格式不一致，导致查询时找不到下注数据。

## 问题分析

### 修复前的问题：
1. **下注保存时使用的key格式：**
   - 方法：`generateUserBetKey()`
   - 格式：`{table_id}:{account_period}:{round_no}:{hand_no}:{wash_code}`
   - 示例：`1:********:1:1:TEST001`

2. **结果录入查询时使用的key格式：**
   - 方法：`generateBetRecordPattern()`
   - 格式：`bet_record:{table_id}:{account_period}:{round_no}:{hand_no}:*`
   - 示例：`bet_record:1:********:1:1:*`

### 问题根源：
- 下注保存时没有使用 `bet_record` 前缀
- 结果录入查询时使用了 `bet_record` 前缀
- 导致查询时完全找不到下注数据

## 修复方案

### 修复后的统一格式：
1. **下注保存时：**
   - 方法：`generateUserBetKey()`
   - 格式：`{table_id}:{account_period}:{round_no}:{hand_no}:{wash_code}`
   - 示例：`1:********:1:1:TEST001`

2. **结果录入查询时：**
   - 方法：`generateUserBetPattern()`（修改后）
   - 格式：`{table_id}:{account_period}:{round_no}:{hand_no}:*`
   - 示例：`1:********:1:1:*`

3. **查询接口：**
   - 方法：`generateUserBetPattern()`
   - 格式：`{table_id}:{account_period}:{round_no}:{hand_no}:*`
   - 示例：`1:********:1:1:*`

## 修复的代码位置

### 文件：`internal/services/bet_service.go`

1. **第701-702行**：修改结果录入接口的查询模式
   ```go
   // 修复前
   redisPattern := s.generateBetRecordPattern(tableID, req.AccountPeriod, req.RoundNo, req.HandNo)
   
   // 修复后
   redisPattern := s.generateUserBetPattern(tableID, req.AccountPeriod, req.RoundNo, req.HandNo)
   ```

2. **第1197-1221行**：标记废弃的方法并添加说明

3. **第1176-1199行**：为现有方法添加详细注释

## 验证方法

### 测试场景：
1. 使用批量下注接口保存下注记录
2. 使用结果录入接口查询该局的下注记录
3. 验证能够正确找到并处理下注数据

### 预期结果：
- 结果录入接口能够成功找到下注记录
- 不再出现"该局没有找到下注记录"的错误
- Redis key格式完全一致

## 影响范围

### 受影响的接口：
1. `EnterResult` - 结果录入接口
2. `QueryBetRecordsFromRedis` - 查询下注记录接口
3. `CreateBatchBetRecord` - 批量下注接口（保存逻辑）

### 向后兼容性：
- 保留了原有的 `generateBetRecordKey` 和 `generateBetRecordPattern` 方法
- 标记为废弃但不删除，确保不会破坏现有代码
