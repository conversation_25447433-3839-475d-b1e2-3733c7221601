-- 插入用户数据
-- 洗码号格式：主号3位数字，分线为主号-分线格式

-- 清空现有数据（可选）
-- DELETE FROM users;

-- 插入主号用户数据
INSERT INTO users (wash_code, account_type, main_code, name, phone, user_type, rebate_rate, is_dragon_tiger, status, memo, operator_remark, create_time, again_create_time, update_time) VALUES
-- 主号用户
('001', 1, '', '张三', '***********', 1, 0.0180, 1, 1, 'VIP客户，主要客户', '优质客户，信用良好', NOW(), NOW(), NOW()),
('002', 1, '', '李四', '***********', 1, 0.0160, 1, 1, '普通客户', '正常客户', NOW(), NOW(), NOW()),
('003', 1, '', '王五', '***********', 2, 0.0200, 0, 1, '返点F客户', '返点客户', NOW(), NOW(), NOW()),
('004', 1, '', '赵六', '***********', 3, 0.0220, 1, 1, '返点W客户', '返点客户', NOW(), NOW(), NOW()),
('005', 1, '', '钱七', '***********', 4, 0.0250, 0, 1, '占成客户', '占成客户', NOW(), NOW(), NOW()),
('006', 1, '', '孙八', '***********', 5, 0.0300, 1, 1, '特殊客户', '特殊客户', NOW(), NOW(), NOW()),
('007', 1, '', '周九', '***********', 1, 0.0170, 1, 1, 'VIP客户', 'VIP客户', NOW(), NOW(), NOW()),
('008', 1, '', '吴十', '13800138008', 1, 0.0150, 0, 1, '普通客户', '普通客户', NOW(), NOW(), NOW()),
('009', 1, '', '郑十一', '13800138009', 2, 0.0210, 1, 1, '返点F客户', '返点客户', NOW(), NOW(), NOW()),
('010', 1, '', '王十二', '13800138010', 3, 0.0230, 0, 1, '返点W客户', '返点客户', NOW(), NOW(), NOW()),

-- 分线用户数据
-- 001的分线
('001-001', 2, '001', '张三分线1', '13800138101', 1, 0.0180, 1, 1, '001主号分线1', '分线客户', NOW(), NOW(), NOW()),
('001-002', 2, '001', '张三分线2', '13800138102', 1, 0.0180, 1, 1, '001主号分线2', '分线客户', NOW(), NOW(), NOW()),
('001-003', 2, '001', '张三分线3', '13800138103', 2, 0.0200, 0, 1, '001主号分线3', '分线客户', NOW(), NOW(), NOW()),

-- 002的分线
('002-001', 2, '002', '李四分线1', '13800138201', 1, 0.0160, 1, 1, '002主号分线1', '分线客户', NOW(), NOW(), NOW()),
('002-002', 2, '002', '李四分线2', '13800138202', 1, 0.0160, 1, 1, '002主号分线2', '分线客户', NOW(), NOW(), NOW()),

-- 003的分线
('003-001', 2, '003', '王五分线1', '13800138301', 2, 0.0200, 0, 1, '003主号分线1', '分线客户', NOW(), NOW(), NOW()),
('003-002', 2, '003', '王五分线2', '13800138302', 2, 0.0200, 0, 1, '003主号分线2', '分线客户', NOW(), NOW(), NOW()),
('003-003', 2, '003', '王五分线3', '13800138303', 3, 0.0220, 1, 1, '003主号分线3', '分线客户', NOW(), NOW(), NOW()),

-- 004的分线
('004-001', 2, '004', '赵六分线1', '13800138401', 3, 0.0220, 1, 1, '004主号分线1', '分线客户', NOW(), NOW(), NOW()),
('004-002', 2, '004', '赵六分线2', '13800138402', 4, 0.0250, 0, 1, '004主号分线2', '分线客户', NOW(), NOW(), NOW()),

-- 005的分线
('005-001', 2, '005', '钱七分线1', '13800138501', 4, 0.0250, 0, 1, '005主号分线1', '分线客户', NOW(), NOW(), NOW()),
('005-002', 2, '005', '钱七分线2', '13800138502', 5, 0.0300, 1, 1, '005主号分线2', '分线客户', NOW(), NOW(), NOW()),

-- 006的分线
('006-001', 2, '006', '孙八分线1', '13800138601', 5, 0.0300, 1, 1, '006主号分线1', '分线客户', NOW(), NOW(), NOW()),
('006-002', 2, '006', '孙八分线2', '13800138602', 1, 0.0180, 1, 1, '006主号分线2', '分线客户', NOW(), NOW(), NOW()),

-- 007的分线
('007-001', 2, '007', '周九分线1', '13800138701', 1, 0.0170, 1, 1, '007主号分线1', '分线客户', NOW(), NOW(), NOW()),
('007-002', 2, '007', '周九分线2', '13800138702', 1, 0.0170, 1, 1, '007主号分线2', '分线客户', NOW(), NOW(), NOW()),
('007-003', 2, '007', '周九分线3', '13800138703', 2, 0.0190, 0, 1, '007主号分线3', '分线客户', NOW(), NOW(), NOW()),

-- 008的分线
('008-001', 2, '008', '吴十分线1', '13800138801', 1, 0.0150, 0, 1, '008主号分线1', '分线客户', NOW(), NOW(), NOW()),
('008-002', 2, '008', '吴十分线2', '13800138802', 1, 0.0150, 0, 1, '008主号分线2', '分线客户', NOW(), NOW(), NOW()),

-- 009的分线
('009-001', 2, '009', '郑十一分线1', '13800138901', 2, 0.0210, 1, 1, '009主号分线1', '分线客户', NOW(), NOW(), NOW()),
('009-002', 2, '009', '郑十一分线2', '13800138902', 2, 0.0210, 1, 1, '009主号分线2', '分线客户', NOW(), NOW(), NOW()),

-- 010的分线
('010-001', 2, '010', '王十二分线1', '13800139001', 3, 0.0230, 0, 1, '010主号分线1', '分线客户', NOW(), NOW(), NOW()),
('010-002', 2, '010', '王十二分线2', '13800139002', 3, 0.0230, 0, 1, '010主号分线2', '分线客户', NOW(), NOW(), NOW()),
('010-003', 2, '010', '王十二分线3', '13800139003', 4, 0.0260, 1, 1, '010主号分线3', '分线客户', NOW(), NOW(), NOW()),

-- 更多主号用户（用于测试）
('011', 1, '', '测试用户1', '13800139101', 1, 0.0180, 1, 1, '测试主号1', '测试客户', NOW(), NOW(), NOW()),
('012', 1, '', '测试用户2', '13800139102', 2, 0.0200, 0, 1, '测试主号2', '测试客户', NOW(), NOW(), NOW()),
('013', 1, '', '测试用户3', '13800139103', 3, 0.0220, 1, 1, '测试主号3', '测试客户', NOW(), NOW(), NOW()),
('014', 1, '', '测试用户4', '13800139104', 4, 0.0250, 0, 1, '测试主号4', '测试客户', NOW(), NOW(), NOW()),
('015', 1, '', '测试用户5', '13800139105', 5, 0.0300, 1, 1, '测试主号5', '测试客户', NOW(), NOW(), NOW()),

-- 冻结状态的用户
('016', 1, '', '冻结用户', '13800139106', 1, 0.0180, 1, 2, '冻结状态用户', '冻结客户', NOW(), NOW(), NOW()),
('017', 1, '', '禁用用户', '13800139107', 1, 0.0180, 1, 3, '禁用状态用户', '禁用客户', NOW(), NOW(), NOW()),

-- 更多分线用户
('011-001', 2, '011', '测试分线1', '13800139201', 1, 0.0180, 1, 1, '011主号分线1', '测试分线', NOW(), NOW(), NOW()),
('012-001', 2, '012', '测试分线2', '13800139202', 2, 0.0200, 0, 1, '012主号分线1', '测试分线', NOW(), NOW(), NOW()),
('013-001', 2, '013', '测试分线3', '***********', 3, 0.0220, 1, 1, '013主号分线1', '测试分线', NOW(), NOW(), NOW()),
('014-001', 2, '014', '测试分线4', '***********', 4, 0.0250, 0, 1, '014主号分线1', '测试分线', NOW(), NOW(), NOW()),
('015-001', 2, '015', '测试分线5', '***********', 5, 0.0300, 1, 1, '015主号分线1', '测试分线', NOW(), NOW(), NOW());

-- 查询验证
SELECT 
    id,
    wash_code,
    CASE account_type
        WHEN 1 THEN '主号'
        WHEN 2 THEN '分线'
    END as account_type_name,
    main_code,
    name,
    phone,
    CASE user_type
        WHEN 1 THEN '公司客户'
        WHEN 2 THEN '返点F'
        WHEN 3 THEN '返点W'
        WHEN 4 THEN '占成客户'
        WHEN 5 THEN '特殊客户'
    END as user_type_name,
    rebate_rate,
    CASE is_dragon_tiger
        WHEN 1 THEN '是'
        WHEN 0 THEN '否'
    END as dragon_tiger_text,
    CASE status
        WHEN 1 THEN '正常'
        WHEN 2 THEN '冻结'
        WHEN 3 THEN '禁用'
    END as status_name,
    memo,
    operator_remark,
    create_time
FROM users
ORDER BY wash_code; 