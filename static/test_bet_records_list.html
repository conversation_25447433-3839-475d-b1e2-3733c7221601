<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>下注记录列表测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
            font-weight: bold;
        }
        
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-row {
            display: flex;
            gap: 15px;
            align-items: end;
        }
        
        .form-row .form-group {
            flex: 1;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        button:hover {
            background-color: #0056b3;
        }
        
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
            font-size: 12px;
        }
        
        .data-table th,
        .data-table td {
            border: 1px solid #ddd;
            padding: 6px;
            text-align: left;
        }
        
        .data-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            position: sticky;
            top: 0;
        }
        
        .data-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 20px;
        }
        
        .pagination button {
            padding: 5px 10px;
            margin: 0;
        }
        
        .pagination span {
            font-weight: bold;
        }
        
        .summary {
            background-color: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        
        .summary-item {
            display: inline-block;
            margin-right: 20px;
            font-weight: bold;
        }
        
        .currency-chip {
            background-color: #ff6b6b;
            color: white;
        }
        
        .currency-cash {
            background-color: #4ecdc4;
            color: white;
        }
        
        .currency-ucode {
            background-color: #45b7d1;
            color: white;
        }
        
        .currency-badge {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: bold;
        }
        
        .table-container {
            max-height: 600px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>下注记录列表测试</h1>
        <div id="status" class="status disconnected">未连接</div>
        
        <div class="form-group">
            <button onclick="connect()">连接</button>
            <button onclick="disconnect()">断开连接</button>
            <button onclick="login()">登录</button>
        </div>
    </div>

    <div class="container">
        <h2>查询下注记录列表</h2>
        <div class="form-row">
            <div class="form-group">
                <label for="accountPeriod">账期 (YYYYMMDD):</label>
                <input type="text" id="accountPeriod" value="********" placeholder="例如: ********">
            </div>
            
            <div class="form-group">
                <label for="roundNo">场次编号 (可选):</label>
                <input type="number" id="roundNo" value="" min="1" placeholder="留空查询所有场次">
            </div>
            
            <div class="form-group">
                <label for="page">页码:</label>
                <input type="number" id="page" value="1" min="1">
            </div>
            
            <div class="form-group">
                <label for="pageSize">每页数量:</label>
                <select id="pageSize">
                    <option value="10">10</option>
                    <option value="20" selected>20</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                </select>
            </div>
            
            <div class="form-group">
                <button onclick="getBetRecordsList()">查询记录</button>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>查询结果</h2>
        <div id="resultSummary"></div>
        <div class="table-container">
            <div id="resultData"></div>
        </div>
        <div id="pagination" class="pagination"></div>
    </div>

    <div class="container">
        <h2>日志</h2>
        <div id="log" class="log"></div>
    </div>

    <script>
        let ws = null;
        let isConnected = false;
        let currentData = null;

        function updateStatus(message, connected) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = connected ? 'status connected' : 'status disconnected';
            isConnected = connected;
        }

        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function connect() {
            if (ws) {
                ws.close();
            }
            
            ws = new WebSocket('ws://************:8080/ws');
            
            ws.onopen = function(event) {
                updateStatus('已连接', true);
                log('WebSocket连接已建立');
            };
            
            ws.onmessage = function(event) {
                const message = JSON.parse(event.data);
                log(`收到消息: ${JSON.stringify(message, null, 2)}`);
                
                if (message.type === 'get_bet_records_list_success') {
                    currentData = message.data;
                    displayBetRecordsList(message.data);
                } else if (message.type === 'get_bet_records_list_error') {
                    log(`查询失败: ${message.data.error}`);
                    document.getElementById('resultData').innerHTML = `<p style="color: red;">查询失败: ${message.data.error}</p>`;
                }
            };
            
            ws.onclose = function(event) {
                updateStatus('连接已断开', false);
                log('WebSocket连接已断开');
            };
            
            ws.onerror = function(error) {
                updateStatus('连接错误', false);
                log('WebSocket错误: ' + error);
            };
        }

        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
            }
        }

        function login() {
            if (!isConnected) {
                alert('请先连接到服务器');
                return;
            }
            
            const loginMessage = {
                type: 'login',
                data: {
                    username: '01001',
                    password: 'admin123'
                }
            };
            
            ws.send(JSON.stringify(loginMessage));
            log('发送登录请求');
        }

        function getBetRecordsList() {
            if (!isConnected) {
                alert('请先连接到服务器');
                return;
            }
            
            const accountPeriod = document.getElementById('accountPeriod').value;
            const roundNo = document.getElementById('roundNo').value;
            const page = parseInt(document.getElementById('page').value);
            const pageSize = parseInt(document.getElementById('pageSize').value);
            
            if (!accountPeriod) {
                alert('请输入账期');
                return;
            }
            
            const data = {
                account_period: accountPeriod,
                page: page,
                page_size: pageSize
            };
            
            if (roundNo) {
                data.round_no = parseInt(roundNo);
            }
            
            const message = {
                type: 'get_bet_records_list',
                data: data
            };
            
            ws.send(JSON.stringify(message));
            log(`发送查询请求: ${JSON.stringify(data)}`);
        }

        function displayBetRecordsList(data) {
            // 显示汇总信息
            const summaryContainer = document.getElementById('resultSummary');
            summaryContainer.innerHTML = `
                <div class="summary">
                    <span class="summary-item">消息: ${data.message}</span>
                    <span class="summary-item">桌台: ${data.table_code}</span>
                    <span class="summary-item">账期: ${data.account_period}</span>
                    ${data.round_no ? `<span class="summary-item">场次: ${data.round_no}</span>` : ''}
                    <span class="summary-item">总记录数: ${data.total_count}</span>
                </div>
            `;
            
            const container = document.getElementById('resultData');
            
            if (!data.records || data.records.length === 0) {
                container.innerHTML = '<p>暂无下注记录数据</p>';
                return;
            }
            
            let html = `
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>场次</th>
                            <th>局号</th>
                            <th>洗码号</th>
                            <th>客户姓名</th>
                            <th>货币类型</th>
                            <th>庄</th>
                            <th>闲</th>
                            <th>和</th>
                            <th>庄对</th>
                            <th>闲对</th>
                            <th>总下注</th>
                            <th>结果</th>
                            <th>输赢</th>
                            <th>输口</th>
                            <th>洗码量</th>
                            <th>创建时间</th>
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            data.records.forEach(record => {
                const currencyClass = getCurrencyClass(record.currency_type);
                html += `
                    <tr>
                        <td>${record.id}</td>
                        <td>${record.round_no}</td>
                        <td>${record.hand_no}</td>
                        <td>${record.wash_code}</td>
                        <td>${record.user_name || '-'}</td>
                        <td><span class="currency-badge ${currencyClass}">${record.currency_type_name}</span></td>
                        <td>${formatAmount(record.banker_amount)}</td>
                        <td>${formatAmount(record.player_amount)}</td>
                        <td>${formatAmount(record.tie_amount)}</td>
                        <td>${formatAmount(record.banker_pair_amount)}</td>
                        <td>${formatAmount(record.player_pair_amount)}</td>
                        <td><strong>${formatAmount(record.total_bet_amount)}</strong></td>
                        <td>${record.win_result || '-'}</td>
                        <td style="color: ${record.win_loss >= 0 ? 'green' : 'red'}">${formatAmount(record.win_loss)}</td>
                        <td>${formatAmount(record.loss)}</td>
                        <td>${formatAmount(record.wash_amount)}</td>
                        <td>${record.create_time}</td>
                    </tr>
                `;
            });
            
            html += `
                    </tbody>
                </table>
            `;
            
            container.innerHTML = html;
            
            // 显示分页控件
            displayPagination(data);
        }

        function displayPagination(data) {
            const container = document.getElementById('pagination');
            
            if (data.total_pages <= 1) {
                container.innerHTML = '';
                return;
            }
            
            let html = '';
            
            // 上一页按钮
            if (data.page > 1) {
                html += `<button onclick="goToPage(${data.page - 1})">上一页</button>`;
            }
            
            // 页码信息
            html += `<span>第 ${data.page} 页，共 ${data.total_pages} 页</span>`;
            
            // 下一页按钮
            if (data.page < data.total_pages) {
                html += `<button onclick="goToPage(${data.page + 1})">下一页</button>`;
            }
            
            container.innerHTML = html;
        }

        function goToPage(page) {
            document.getElementById('page').value = page;
            getBetRecordsList();
        }

        function getCurrencyClass(currencyType) {
            switch(currencyType) {
                case 1: return 'currency-chip';
                case 2: return 'currency-cash';
                case 3: return 'currency-ucode';
                default: return '';
            }
        }

        function formatAmount(amount) {
            if (amount === 0) return '0.00';
            return amount.toFixed(2);
        }

        // 页面加载时自动连接
        window.onload = function() {
            // 可以在这里自动连接
        };
    </script>
</body>
</html>
