<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>点码列表测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h2 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        .data-table th, .data-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .data-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .data-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .currency-chip { color: #007bff; font-weight: bold; }
        .currency-cash { color: #28a745; font-weight: bold; }
        .currency-ucode { color: #ffc107; font-weight: bold; }
        .compare-correct { color: #28a745; font-weight: bold; }
        .compare-more { color: #dc3545; font-weight: bold; }
        .compare-less { color: #fd7e14; font-weight: bold; }
        .compare-none { color: #6c757d; }
    </style>
</head>
<body>
    <div class="container">
        <h2>WebSocket 连接</h2>
        <div id="status" class="status disconnected">未连接</div>
        <button onclick="connect()">连接</button>
        <button onclick="disconnect()">断开连接</button>
    </div>

    <div class="container">
        <h2>用户登录</h2>
        <div class="form-group">
            <label for="username">用户名:</label>
            <input type="text" id="username" value="admin" placeholder="请输入用户名">
        </div>
        <div class="form-group">
            <label for="password">密码:</label>
            <input type="password" id="password" value="123456" placeholder="请输入密码">
        </div>
        <button onclick="login()">登录</button>
    </div>

    <div class="container">
        <h2>点码列表查询</h2>
        <p><strong>说明：</strong>客户端无需传递查询条件，服务端自动根据IP获取当前桌台的所有点码记录</p>
        <button onclick="getSettlementList()">获取点码列表</button>
    </div>

    <div class="container">
        <h2>查询结果</h2>
        <div id="resultData"></div>
    </div>

    <div class="container">
        <h2>日志</h2>
        <div id="log" class="log"></div>
    </div>

    <script>
        let ws = null;
        let currentToken = null;

        function connect() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                addMessage('WebSocket已连接', 'info');
                return;
            }

            ws = new WebSocket('ws://localhost:8080/ws');
            
            ws.onopen = function(event) {
                document.getElementById('status').textContent = '已连接';
                document.getElementById('status').className = 'status connected';
                addMessage('WebSocket连接成功', 'success');
            };
            
            ws.onmessage = function(event) {
                const message = JSON.parse(event.data);
                addMessage('收到消息: ' + JSON.stringify(message, null, 2), 'info');
                handleMessage(message);
            };
            
            ws.onclose = function(event) {
                document.getElementById('status').textContent = '连接已关闭';
                document.getElementById('status').className = 'status disconnected';
                addMessage('WebSocket连接关闭', 'warning');
            };
            
            ws.onerror = function(error) {
                addMessage('WebSocket错误: ' + error, 'error');
            };
        }

        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
            }
        }

        function sendMessage(message) {
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify(message));
                addMessage('发送消息: ' + JSON.stringify(message, null, 2), 'sent');
            } else {
                addMessage('WebSocket未连接', 'error');
            }
        }

        function addMessage(message, type) {
            const log = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const div = document.createElement('div');
            div.innerHTML = `[${timestamp}] ${message}`;
            
            switch(type) {
                case 'error':
                    div.style.color = 'red';
                    break;
                case 'success':
                    div.style.color = 'green';
                    break;
                case 'warning':
                    div.style.color = 'orange';
                    break;
                case 'sent':
                    div.style.color = 'blue';
                    break;
                default:
                    div.style.color = 'black';
            }
            
            log.appendChild(div);
            log.scrollTop = log.scrollHeight;
        }

        function handleMessage(message) {
            switch(message.type) {
                case 'login_success':
                    currentToken = message.data.token;
                    addMessage('登录成功，令牌已保存', 'success');
                    break;
                case 'login_error':
                    addMessage('登录失败: ' + message.data.error, 'error');
                    break;
                case 'get_settlement_list_success':
                    displaySettlementList(message.data);
                    break;
                case 'get_settlement_list_error':
                    addMessage('获取点码列表失败: ' + message.data.error, 'error');
                    break;
            }
        }

        function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            if (!username || !password) {
                addMessage('请输入用户名和密码', 'error');
                return;
            }
            
            sendMessage({
                type: 'login',
                data: {
                    username: username,
                    password: password
                }
            });
        }

        function getSettlementList() {
            if (!currentToken) {
                addMessage('请先登录', 'error');
                return;
            }
            
            sendMessage({
                type: 'get_settlement_list'
            });
        }

        function displaySettlementList(data) {
            const container = document.getElementById('resultData');
            
            if (!data.records || data.records.length === 0) {
                container.innerHTML = '<p>暂无点码记录</p>';
                return;
            }
            
            let html = `
                <h3>查询结果</h3>
                <p><strong>消息：</strong>${data.message}</p>
                <p><strong>桌台ID：</strong>${data.table_id}</p>
                <p><strong>桌台编号：</strong>${data.table_code}</p>
                <p><strong>记录总数：</strong>${data.total_count}</p>
                
                <h3>详细记录</h3>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>场次</th>
                            <th>账期</th>
                            <th>货币类型</th>
                            <th>总额</th>
                            <th>比对结果</th>
                            <th>比对类型</th>
                            <th>操作人</th>
                            <th>创建时间</th>
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            data.records.forEach(record => {
                const currencyClass = getCurrencyClass(record.currency_type);
                const compareClass = getCompareClass(record.compare_type);
                
                html += `
                    <tr>
                        <td>${record.id}</td>
                        <td>${record.shoe_no}</td>
                        <td>${record.account_period}</td>
                        <td class="${currencyClass}">${record.currency_name}</td>
                        <td>${record.total_amount.toFixed(2)}</td>
                        <td>${record.compare_result.toFixed(2)}</td>
                        <td class="${compareClass}">${record.compare_type_name}</td>
                        <td>${record.operator}</td>
                        <td>${record.create_time}</td>
                    </tr>
                `;
            });
            
            html += `
                    </tbody>
                </table>
            `;
            
            container.innerHTML = html;
        }

        function getCurrencyClass(currencyType) {
            switch(currencyType) {
                case 1: return 'currency-chip';
                case 2: return 'currency-cash';
                case 3: return 'currency-ucode';
                default: return '';
            }
        }

        function getCompareClass(compareType) {
            switch(compareType) {
                case 1: return 'compare-more';
                case 2: return 'compare-less';
                case 3: return 'compare-correct';
                default: return 'compare-none';
            }
        }

        // 页面加载时自动连接
        window.onload = function() {
            // 可以在这里自动连接
        };
    </script>
</body>
</html>
