<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket API 测试工具</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .status-panel {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-connected {
            background-color: #28a745;
        }
        .status-disconnected {
            background-color: #dc3545;
        }
        .control-panel {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background-color: #0056b3;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-success:hover {
            background-color: #1e7e34;
        }
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        .btn-warning:hover {
            background-color: #e0a800;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .btn-danger:hover {
            background-color: #c82333;
        }
        .btn-info {
            background-color: #17a2b8;
            color: white;
        }
        .btn-info:hover {
            background-color: #138496;
        }
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        .section {
            margin-bottom: 30px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
        }
        .section h2 {
            margin-top: 0;
            color: #495057;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #495057;
        }
        .form-group input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        .form-group input:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }
        .form-inline {
            display: flex;
            gap: 10px;
            align-items: end;
        }
        .form-inline .form-group {
            flex: 1;
            margin-bottom: 0;
        }
        .messages {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            background-color: #f8f9fa;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
        }
        .message {
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 4px;
            word-wrap: break-word;
        }
        .message-send {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
        }
        .message-receive {
            background-color: #f3e5f5;
            border-left: 4px solid #9c27b0;
        }
        .message-error {
            background-color: #ffebee;
            border-left: 4px solid #f44336;
        }
        .message-success {
            background-color: #e8f5e8;
            border-left: 4px solid #4caf50;
        }
        .timestamp {
            color: #666;
            font-size: 11px;
            margin-right: 10px;
        }
        .user-info {
            background-color: #e8f5e8;
            border: 1px solid #c3e6c3;
            border-radius: 5px;
            padding: 15px;
            margin-top: 10px;
        }
        .user-info h3 {
            margin-top: 0;
            color: #2e7d32;
        }
        .user-info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        .user-info-item {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
            border-bottom: 1px solid #ddd;
        }
        .user-info-item:last-child {
            border-bottom: none;
        }
        .user-info-label {
            font-weight: bold;
            color: #495057;
        }
        .user-info-value {
            color: #6c757d;
        }
        .table-info {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin-top: 10px;
        }
        .table-info h3 {
            margin-top: 0;
            color: #856404;
        }
        .table-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 10px;
        }
        .table-item {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
            border-bottom: 1px solid #ddd;
        }
        .table-item:last-child {
            border-bottom: none;
        }
        .table-label {
            font-weight: bold;
            color: #495057;
        }
        .table-value {
            color: #6c757d;
        }
        .bets-section {
            margin-top: 15px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .bets-section h4 {
            margin-top: 0;
            color: #495057;
        }
        .bet-item {
            background-color: white;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 10px;
        }
        .bet-item:last-child {
            margin-bottom: 0;
        }
        .login-status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .login-status.authenticated {
            background-color: #d4edda;
            color: #155724;
        }
        .login-status.unauthenticated {
            background-color: #f8d7da;
            color: #721c24;
        }
        .token-display {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            word-break: break-all;
        }
        .customer-info {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 20px;
            margin-top: 20px;
        }
        .customer-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
        }
        .customer-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .customer-item:last-child {
            border-bottom: none;
        }
        .customer-label {
            font-weight: bold;
            color: #495057;
            min-width: 120px;
        }
        .customer-value {
            color: #6c757d;
            text-align: right;
            flex: 1;
        }
        .status-normal {
            color: #28a745;
            font-weight: bold;
        }
        .status-frozen {
            color: #ffc107;
            font-weight: bold;
        }
        .status-disabled {
            color: #dc3545;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebSocket API 测试工具</h1>
        
        <!-- 连接状态面板 -->
        <div class="status-panel">
            <h2>连接状态</h2>
            <div>
                <span class="status-indicator" id="statusIndicator"></span>
                <span id="statusText">未连接</span>
                <span id="loginStatus" class="login-status unauthenticated">未登录</span>
            </div>
            <div class="control-panel">
                <button class="btn btn-primary" id="connectBtn">连接</button>
                <button class="btn btn-danger" id="disconnectBtn" disabled>断开连接</button>
                <button class="btn btn-warning" id="clearBtn">清空日志</button>
            </div>
        </div>

        <!-- 用户认证区域 -->
        <div class="section">
            <h2>用户认证</h2>
            <div class="form-inline">
                <div class="form-group">
                    <label for="username">用户名</label>
                    <input type="text" id="username" placeholder="请输入用户名" value="01001">
                </div>
                <div class="form-group">
                    <label for="password">密码</label>
                    <input type="password" id="password" placeholder="请输入密码" value="123456">
                </div>
                <button class="btn btn-success" id="loginBtn" disabled>登录</button>
                <button class="btn btn-warning" id="logoutBtn" disabled>登出</button>
            </div>
            
            <div class="form-inline" style="margin-top: 15px;">
                <div class="form-group">
                    <label for="oldPassword">原密码</label>
                    <input type="password" id="oldPassword" placeholder="请输入原密码">
                </div>
                <div class="form-group">
                    <label for="newPassword">新密码</label>
                    <input type="password" id="newPassword" placeholder="请输入新密码">
                </div>
                <button class="btn btn-info" id="changePasswordBtn" disabled>修改密码</button>
            </div>
            
            <div class="control-panel" style="margin-top: 15px;">
                <button class="btn btn-info" id="getUserInfoBtn" disabled>获取用户信息</button>
                <button class="btn btn-info" id="verifyTokenBtn" disabled>验证令牌</button>

            </div>
            
            <div id="tokenDisplay" class="token-display" style="display: none;">
                <strong>当前令牌：</strong><br>
                <span id="tokenValue"></span>
            </div>
        </div>

        <!-- 桌台信息区域 -->
        <div class="section">
            <h2>桌台信息</h2>
            <div class="form-inline">
                <button class="btn btn-primary" id="getTableInfoBtn" disabled>获取桌台信息</button>
                <button class="btn btn-success" id="getLatestTablesStartBtn" disabled>获取最新开台数据</button>
            </div>
        </div>

        <!-- 客户信息 -->
        <div class="section">
            <h2>客户信息</h2>
            <div class="form-inline">
                <div class="form-group">
                    <label for="washCode">洗码号</label>
                    <input type="text" id="washCode" placeholder="请输入洗码号">
                </div>
                <button class="btn btn-success" id="getUserByWashCodeBtn" disabled>获取客户信息</button>
            </div>
        </div>

        <!-- 消息日志 -->
        <div class="section">
            <h2>消息日志</h2>
            <div class="messages" id="messages"></div>
        </div>

        <!-- 用户信息显示区域 -->
        <div id="userInfoSection" class="user-info" style="display: none;">
            <h3>用户信息</h3>
            <div class="user-info-grid" id="userInfoGrid"></div>
        </div>

        <!-- 桌台信息显示区域 -->
        <div id="tableInfoSection" class="table-info" style="display: none;">
            <h3>桌台信息</h3>
            <div class="table-details" id="tableDetails"></div>
            <div class="bets-section" id="betsSection" style="display: none;">
                <h4>下注配置</h4>
                <div id="betsList"></div>
            </div>
        </div>

        <!-- 客户信息显示区域 -->
        <div id="customerInfoSection" class="customer-info" style="display: none;">
            <h3>客户信息</h3>
            <div class="customer-details" id="customerDetails"></div>
        </div>
    </div>

    <script>
        let ws = null;
        let currentToken = null;
        let isAuthenticated = false;

        // DOM 元素
        const statusIndicator = document.getElementById('statusIndicator');
        const statusText = document.getElementById('statusText');
        const loginStatus = document.getElementById('loginStatus');
        const connectBtn = document.getElementById('connectBtn');
        const disconnectBtn = document.getElementById('disconnectBtn');
        const clearBtn = document.getElementById('clearBtn');
        const loginBtn = document.getElementById('loginBtn');
        const logoutBtn = document.getElementById('logoutBtn');
        const changePasswordBtn = document.getElementById('changePasswordBtn');
        const getUserInfoBtn = document.getElementById('getUserInfoBtn');
        const verifyTokenBtn = document.getElementById('verifyTokenBtn');
        const getTableInfoBtn = document.getElementById('getTableInfoBtn');
        const getLatestTablesStartBtn = document.getElementById('getLatestTablesStartBtn');
        const getUserByWashCodeBtn = document.getElementById('getUserByWashCodeBtn');
        const messages = document.getElementById('messages');
        const userInfoSection = document.getElementById('userInfoSection');

        const tableInfoSection = document.getElementById('tableInfoSection');
        const customerInfoSection = document.getElementById('customerInfoSection');
        const tokenDisplay = document.getElementById('tokenDisplay');
        const tokenValue = document.getElementById('tokenValue');

        // 连接WebSocket
        function connect() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                return;
            }

            ws = new WebSocket('ws://192.168.1.44:8080/ws');

            ws.onopen = function() {
                updateConnectionStatus(true);
                addMessage('已连接到WebSocket服务器', 'success');
                
                // 启用连接后的按钮
                loginBtn.disabled = false;
                getTableInfoBtn.disabled = false;
                getLatestTablesStartBtn.disabled = false;
                getUserByWashCodeBtn.disabled = false;

            };

            ws.onmessage = function(event) {
                const message = JSON.parse(event.data);
                addMessage('收到消息: ' + JSON.stringify(message, null, 2), 'receive');
                handleMessage(message);
            };

            ws.onclose = function() {
                updateConnectionStatus(false);
                addMessage('WebSocket连接已关闭', 'error');
                
                // 重置认证状态
                updateAuthStatus(false);
                
                // 禁用所有功能按钮
                disableAllButtons();
            };

            ws.onerror = function(error) {
                addMessage('WebSocket错误: ' + error, 'error');
            };
        }

        // 断开连接
        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
            }
        }

        // 更新连接状态
        function updateConnectionStatus(connected) {
            if (connected) {
                statusIndicator.className = 'status-indicator status-connected';
                statusText.textContent = '已连接';
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
            } else {
                statusIndicator.className = 'status-indicator status-disconnected';
                statusText.textContent = '未连接';
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
            }
        }

        // 更新认证状态
        function updateAuthStatus(authenticated, token = null) {
            isAuthenticated = authenticated;
            currentToken = token;
            
            if (authenticated) {
                loginStatus.className = 'login-status authenticated';
                loginStatus.textContent = '已登录';
                loginBtn.disabled = true;
                logoutBtn.disabled = false;
                changePasswordBtn.disabled = false;
                getUserInfoBtn.disabled = false;
                verifyTokenBtn.disabled = false;

                
                if (token) {
                    tokenDisplay.style.display = 'block';
                    tokenValue.textContent = token;
                }
            } else {
                loginStatus.className = 'login-status unauthenticated';
                loginStatus.textContent = '未登录';
                loginBtn.disabled = false;
                logoutBtn.disabled = true;
                changePasswordBtn.disabled = true;
                getUserInfoBtn.disabled = true;
                verifyTokenBtn.disabled = true;

                
                tokenDisplay.style.display = 'none';
                userInfoSection.style.display = 'none';
                currentToken = null;
            }
        }

        // 禁用所有按钮
        function disableAllButtons() {
            loginBtn.disabled = true;
            logoutBtn.disabled = true;
            changePasswordBtn.disabled = true;
            getUserInfoBtn.disabled = true;
            verifyTokenBtn.disabled = true;
            getTableInfoBtn.disabled = true;
            getLatestTablesStartBtn.disabled = true;
            getUserByWashCodeBtn.disabled = true;
        }

        // 发送消息
        function sendMessage(message) {
            if (ws && ws.readyState === WebSocket.OPEN) {
                const messageStr = JSON.stringify(message);
                ws.send(messageStr);
                addMessage('发送消息: ' + JSON.stringify(message, null, 2), 'send');
            } else {
                addMessage('WebSocket未连接', 'error');
            }
        }

        // 添加消息到日志
        function addMessage(text, type = 'info') {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message message-${type}`;
            
            const timestamp = new Date().toLocaleTimeString();
            messageDiv.innerHTML = `<span class="timestamp">${timestamp}</span>${text}`;
            
            messages.appendChild(messageDiv);
            messages.scrollTop = messages.scrollHeight;
        }

        // 处理收到的消息
        function handleMessage(message) {
            switch(message.type) {
                case 'login_success':
                    updateAuthStatus(true, message.data.token);
                    displayUserInfo(message.data.user_info);
                    addMessage('登录成功！', 'success');
                    break;
                    
                case 'login_error':
                    addMessage('登录失败: ' + message.data.error, 'error');
                    break;
                    
                case 'logout_success':
                    updateAuthStatus(false);
                    addMessage('登出成功！', 'success');
                    break;
                    
                case 'user_info_success':
                    displayUserInfo(message.data.user_info);
                    addMessage('用户信息获取成功！', 'success');
                    break;
                    
                case 'user_info_error':
                    addMessage('获取用户信息失败: ' + message.data.error, 'error');
                    break;
                    
                case 'change_password_success':
                    addMessage('密码修改成功！', 'success');
                    break;
                    
                case 'change_password_error':
                    addMessage('密码修改失败: ' + message.data.error, 'error');
                    break;
                    
                case 'verify_token_success':
                    addMessage('令牌验证成功！用户: ' + message.data.username, 'success');
                    break;
                    
                case 'verify_token_error':
                    addMessage('令牌验证失败: ' + message.data.error, 'error');
                    break;
                    

                    
                case 'table_info_success':

                    displayTableInfo(message.data.table);
                    addMessage('桌台信息获取成功！', 'success');
                    break;
                    
                case 'table_info_error':

                    addMessage('桌台信息获取失败: ' + message.data.error, 'error');
                    break;
                    
                case 'get_user_by_wash_code_success':
                    displayCustomerInfo(message.data.user_info);
                    addMessage('客户信息获取成功！', 'success');
                    break;
                    
                case 'get_user_by_wash_code_error':
                    addMessage('客户信息获取失败: ' + message.data.error, 'error');
                    break;
                    
                case 'get_latest_tables_start_success':
                    addMessage('最新开台数据获取成功！', 'success');
                    addMessage('客户端IP: ' + message.data.client_ip, 'info');
                    break;
                    
                case 'get_latest_tables_start_error':
                    addMessage('最新开台数据获取失败: ' + message.data.error, 'error');
                    addMessage('客户端IP: ' + message.data.client_ip, 'info');
                    break;
            }
        }

        // 显示用户信息
        function displayUserInfo(userInfo) {
            const userInfoGrid = document.getElementById('userInfoGrid');
            userInfoGrid.innerHTML = '';
            
            const fields = [
                { key: 'id', label: '用户ID' },
                { key: 'username', label: '用户名' },
                { key: 'realname', label: '真实姓名' },
                { key: 'nickname', label: '昵称' },
                { key: 'gender_name', label: '性别' },
                { key: 'mobile', label: '手机号码' },
                { key: 'email', label: '邮箱' },
                { key: 'status_name', label: '状态' },
                { key: 'login_ip', label: '登录IP' },
                { key: 'login_time', label: '登录时间' },
                { key: 'create_time', label: '创建时间' }
            ];
            
            fields.forEach(field => {
                const item = document.createElement('div');
                item.className = 'user-info-item';
                
                let value = userInfo[field.key] || '';
                if (field.key === 'login_time' || field.key === 'create_time') {
                    value = value ? new Date(value * 1000).toLocaleString() : '';
                }
                
                item.innerHTML = `
                    <span class="user-info-label">${field.label}:</span>
                    <span class="user-info-value">${value}</span>
                `;
                
                userInfoGrid.appendChild(item);
            });
            
            userInfoSection.style.display = 'block';
        }

        // 显示桌台信息
        function displayTableInfo(tableInfo) {
            const tableDetails = document.getElementById('tableDetails');
            const betsSection = document.getElementById('betsSection');
            const betsList = document.getElementById('betsList');
            
            tableDetails.innerHTML = '';
            betsList.innerHTML = '';
            
            const fields = [
                { key: 'table_code', label: '桌台编号' },
                { key: 'table_name', label: '桌台名称' },
                { key: 'game_type_name', label: '游戏类型' },
                { key: 'table_ip', label: '桌台IP' },
                { key: 'video_url', label: '视频地址' },
                { key: 'channel_name', label: '业务通道' },
                { key: 'wash_rate', label: '洗码率' },
                { key: 'max_bet_u', label: 'U码最大下注' },
                { key: 'max_bet_cash', label: '现金最大下注' },
                { key: 'max_bet_chips', label: '筹码最大下注' },
                { key: 'tie_rate', label: '龙虎和底返' },
                { key: 'status_name', label: '状态' },
                { key: 'memo', label: '备注' },
                { key: 'create_time', label: '创建时间' }
            ];
            
            fields.forEach(field => {
                const item = document.createElement('div');
                item.className = 'table-item';
                
                let value = tableInfo[field.key] || '';
                if (field.key === 'wash_rate' || field.key === 'tie_rate') {
                    value = value ? (value * 100).toFixed(2) + '%' : '';
                } else if (field.key.includes('max_bet')) {
                    value = value ? Number(value).toLocaleString() : '';
                }
                
                item.innerHTML = `
                    <span class="table-label">${field.label}:</span>
                    <span class="table-value">${value}</span>
                `;
                
                tableDetails.appendChild(item);
            });
            
            // 显示下注配置
            if (tableInfo.bets && tableInfo.bets.length > 0) {
                tableInfo.bets.forEach(bet => {
                    const betItem = document.createElement('div');
                    betItem.className = 'bet-item';
                    
                    betItem.innerHTML = `
                        <div><strong>下注区域:</strong> ${bet.bet_area}</div>
                        <div><strong>赔率:</strong> ${bet.odds}</div>
                        <div><strong>U码最大下注:</strong> ${Number(bet.max_bet_u).toLocaleString()}</div>
                        <div><strong>现金最大下注:</strong> ${Number(bet.max_bet_cash).toLocaleString()}</div>
                        <div><strong>筹码最大下注:</strong> ${Number(bet.max_bet_chips).toLocaleString()}</div>
                        <div><strong>创建时间:</strong> ${bet.create_time}</div>
                    `;
                    
                    betsList.appendChild(betItem);
                });
                
                betsSection.style.display = 'block';
            } else {
                betsSection.style.display = 'none';
            }
            
            tableInfoSection.style.display = 'block';
        }

        // 用户登录
        function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            if (!username || !password) {
                addMessage('请输入用户名和密码', 'error');
                return;
            }
            
            sendMessage({
                type: 'login',
                data: {
                    username: username,
                    password: password
                }
            });
        }

        // 用户登出
        function logout() {
            sendMessage({
                type: 'logout'
            });
        }

        // 获取用户信息
        function getUserInfo() {
            sendMessage({
                type: 'get_user_info'
            });
        }

        // 修改密码
        function changePassword() {
            const oldPassword = document.getElementById('oldPassword').value;
            const newPassword = document.getElementById('newPassword').value;
            
            if (!oldPassword || !newPassword) {
                addMessage('请输入原密码和新密码', 'error');
                return;
            }
            
            sendMessage({
                type: 'change_password',
                data: {
                    old_password: oldPassword,
                    new_password: newPassword
                }
            });
        }

        // 验证令牌
        function verifyToken() {
            if (!currentToken) {
                addMessage('当前没有令牌', 'error');
                return;
            }
            
            sendMessage({
                type: 'verify_token',
                data: {
                    token: currentToken
                }
            });
        }

        // 获取桌台信息
        function getTableInfo() {
            sendMessage({
                type: 'get_table_info'
            });
        }

        // 获取最新开台数据
        function getLatestTablesStart() {
            sendMessage({
                type: 'get_latest_tables_start'
            });
        }

        // 获取客户信息
        function getUserByWashCode() {
            const washCode = document.getElementById('washCode').value;
            
            if (!washCode) {
                addMessage('请输入洗码号', 'error');
                return;
            }
            
            sendMessage({
                type: 'get_user_by_wash_code',
                data: {
                    wash_code: washCode
                }
            });
        }

        // 显示客户信息
        function displayCustomerInfo(customerInfo) {
            const customerDetails = document.getElementById('customerDetails');
            customerDetails.innerHTML = '';
            
            const fields = [
                { key: 'wash_code', label: '洗码号' },
                { key: 'name', label: '客户姓名' },
                { key: 'phone', label: '联系电话' },
                { key: 'account_type_name', label: '账号类型' },
                { key: 'main_code', label: '主号洗码号' },
                { key: 'user_type_name', label: '客户类型' },
                { key: 'rebate_rate_text', label: '返点比例' },
                { key: 'dragon_tiger_text', label: '龙虎洗码' },
                { key: 'status_name', label: '账号状态' },
                { key: 'memo', label: '客户备注' },
                { key: 'operator_remark', label: '操作备注' },
                { key: 'create_time', label: '创建时间' },
                { key: 'again_create_time', label: '重开户时间' },
                { key: 'update_time', label: '更新时间' }
            ];
            
            fields.forEach(field => {
                const item = document.createElement('div');
                item.className = 'customer-item';
                
                let value = customerInfo[field.key] || '';
                let valueClass = 'customer-value';
                
                // 为状态添加特殊样式
                if (field.key === 'status_name') {
                    switch (customerInfo.status) {
                        case 1:
                            valueClass += ' status-normal';
                            break;
                        case 2:
                            valueClass += ' status-frozen';
                            break;
                        case 3:
                            valueClass += ' status-disabled';
                            break;
                    }
                }
                
                item.innerHTML = `
                    <span class="customer-label">${field.label}:</span>
                    <span class="${valueClass}">${value}</span>
                `;
                
                customerDetails.appendChild(item);
            });
            
            customerInfoSection.style.display = 'block';
        }

        // 清空日志
        function clearMessages() {
            messages.innerHTML = '';
        }

        // 事件监听器
        connectBtn.addEventListener('click', connect);
        disconnectBtn.addEventListener('click', disconnect);
        clearBtn.addEventListener('click', clearMessages);
        loginBtn.addEventListener('click', login);
        logoutBtn.addEventListener('click', logout);
        getUserInfoBtn.addEventListener('click', getUserInfo);
        changePasswordBtn.addEventListener('click', changePassword);
        verifyTokenBtn.addEventListener('click', verifyToken);
        getTableInfoBtn.addEventListener('click', getTableInfo);
        getLatestTablesStartBtn.addEventListener('click', getLatestTablesStart);
        getUserByWashCodeBtn.addEventListener('click', getUserByWashCode);

        // 回车键登录
        document.getElementById('password').addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !loginBtn.disabled) {
                login();
            }
        });

        // 回车键获取客户信息
        document.getElementById('washCode').addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !getUserByWashCodeBtn.disabled) {
                getUserByWashCode();
            }
        });

        // 页面加载完成后自动连接
        window.addEventListener('load', function() {
            // 可以在这里添加自动连接逻辑
            // connect();
        });

    </script>
</body>
</html> 