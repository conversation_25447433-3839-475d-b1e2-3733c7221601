<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>获取条口记录结果测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
            font-weight: bold;
        }
        
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        button:hover {
            background-color: #0056b3;
        }
        
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        
        .data-table th,
        .data-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        
        .data-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        
        .data-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .result-item {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        
        .result-label {
            font-weight: bold;
            color: #666;
        }
        
        .result-value {
            color: #333;
        }
        
        .pearl-road {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(30px, 1fr));
            gap: 2px;
            margin-top: 10px;
            max-width: 600px;
        }
        
        .pearl-item {
            width: 30px;
            height: 30px;
            border: 1px solid #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
        
        .pearl-banker {
            background-color: #ff6b6b;
            color: white;
        }
        
        .pearl-player {
            background-color: #4ecdc4;
            color: white;
        }
        
        .pearl-tie {
            background-color: #45b7d1;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>获取条口记录结果测试</h1>
        <div id="status" class="status disconnected">未连接</div>
        
        <div class="form-group">
            <button onclick="connect()">连接</button>
            <button onclick="disconnect()">断开连接</button>
            <button onclick="login()">登录</button>
        </div>
    </div>

    <div class="container">
        <h2>查询条口记录结果</h2>
        <div class="form-group">
            <label for="accountPeriod">账期 (YYYYMMDD):</label>
            <input type="text" id="accountPeriod" value="********" placeholder="例如: ********">
        </div>
        
        <div class="form-group">
            <label for="shoeNo">场次编号:</label>
            <input type="number" id="shoeNo" value="1" min="1" placeholder="例如: 1">
        </div>
        
        <button onclick="getHandRecordsResult()">获取条口记录结果</button>
    </div>

    <div class="container">
        <h2>查询结果</h2>
        <div id="resultData"></div>
    </div>

    <div class="container">
        <h2>露珠图预览</h2>
        <div id="pearlRoad"></div>
    </div>

    <div class="container">
        <h2>日志</h2>
        <div id="log" class="log"></div>
    </div>

    <script>
        let ws = null;
        let isConnected = false;

        function updateStatus(message, connected) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = connected ? 'status connected' : 'status disconnected';
            isConnected = connected;
        }

        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function connect() {
            if (ws) {
                ws.close();
            }
            
            ws = new WebSocket('ws://************:8080/ws');
            
            ws.onopen = function(event) {
                updateStatus('已连接', true);
                log('WebSocket连接已建立');
            };
            
            ws.onmessage = function(event) {
                const message = JSON.parse(event.data);
                log(`收到消息: ${JSON.stringify(message, null, 2)}`);
                
                if (message.type === 'get_hand_records_result_success') {
                    displayHandRecordsResult(message.data);
                } else if (message.type === 'get_hand_records_result_error') {
                    log(`查询失败: ${message.data.error}`);
                }
            };
            
            ws.onclose = function(event) {
                updateStatus('连接已断开', false);
                log('WebSocket连接已断开');
            };
            
            ws.onerror = function(error) {
                updateStatus('连接错误', false);
                log('WebSocket错误: ' + error);
            };
        }

        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
            }
        }

        function login() {
            if (!isConnected) {
                alert('请先连接到服务器');
                return;
            }
            
            const loginMessage = {
                type: 'login',
                data: {
                    username: '01001',
                    password: 'admin123'
                }
            };
            
            ws.send(JSON.stringify(loginMessage));
            log('发送登录请求');
        }

        function getHandRecordsResult() {
            if (!isConnected) {
                alert('请先连接到服务器');
                return;
            }
            
            const accountPeriod = document.getElementById('accountPeriod').value;
            const shoeNo = parseInt(document.getElementById('shoeNo').value);
            
            if (!accountPeriod) {
                alert('请输入账期');
                return;
            }
            
            if (!shoeNo || shoeNo <= 0) {
                alert('请输入有效的场次编号');
                return;
            }
            
            const message = {
                type: 'get_hand_records_result',
                data: {
                    account_period: accountPeriod,
                    shoe_no: shoeNo
                }
            };
            
            ws.send(JSON.stringify(message));
            log(`发送查询请求: 账期=${accountPeriod}, 场次=${shoeNo}`);
        }

        function displayHandRecordsResult(data) {
            const container = document.getElementById('resultData');
            
            if (!data.records || data.records.length === 0) {
                container.innerHTML = '<p>暂无条口记录数据</p>';
                return;
            }
            
            let html = `
                <div class="result-item">
                    <span class="result-label">消息:</span>
                    <span class="result-value">${data.message}</span>
                </div>
                <div class="result-item">
                    <span class="result-label">桌台ID:</span>
                    <span class="result-value">${data.table_id}</span>
                </div>
                <div class="result-item">
                    <span class="result-label">桌台编号:</span>
                    <span class="result-value">${data.table_code}</span>
                </div>
                <div class="result-item">
                    <span class="result-label">账期:</span>
                    <span class="result-value">${data.account_period}</span>
                </div>
                <div class="result-item">
                    <span class="result-label">场次:</span>
                    <span class="result-value">${data.shoe_no}</span>
                </div>
                <div class="result-item">
                    <span class="result-label">记录总数:</span>
                    <span class="result-value">${data.total_count}</span>
                </div>
                
                <h3>详细记录</h3>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>局号</th>
                            <th>结果</th>
                            <th>创建时间</th>
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            data.records.forEach(record => {
                html += `
                    <tr>
                        <td>${record.hand_no}</td>
                        <td>${record.result_1}</td>
                        <td>${record.create_time}</td>
                    </tr>
                `;
            });
            
            html += `
                    </tbody>
                </table>
            `;
            
            container.innerHTML = html;
            
            // 生成露珠图
            generatePearlRoad(data.records);
        }

        function generatePearlRoad(records) {
            const container = document.getElementById('pearlRoad');
            
            if (!records || records.length === 0) {
                container.innerHTML = '<p>暂无数据生成露珠图</p>';
                return;
            }
            
            let html = '<h3>露珠图</h3><div class="pearl-road">';
            
            records.forEach(record => {
                const result = record.result_1;
                let className = 'pearl-item';
                let text = result;
                
                if (result === '庄') {
                    className += ' pearl-banker';
                    text = '庄';
                } else if (result === '闲') {
                    className += ' pearl-player';
                    text = '闲';
                } else if (result === '和') {
                    className += ' pearl-tie';
                    text = '和';
                }
                
                html += `<div class="${className}">${text}</div>`;
            });
            
            html += '</div>';
            container.innerHTML = html;
        }

        // 页面加载时自动连接
        window.onload = function() {
            // 可以在这里自动连接
        };
    </script>
</body>
</html>
