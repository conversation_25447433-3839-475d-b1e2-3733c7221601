// @title 记账系统API
// @version 1.0
// @description 基于WebSocket的记账系统后端API
// @host localhost:8080
// @BasePath /
package main

import (
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"

	"accounting_enter/internal/config"
	"accounting_enter/internal/database"
	"accounting_enter/internal/routes"
	ws "accounting_enter/internal/websocket"
)

func main() {
	// 加载配置
	if err := config.LoadConfig("config/config.yaml"); err != nil {
		log.Fatalf("配置加载失败: %v", err)
	}

	// 初始化数据库连接
	if err := database.InitMySQL(); err != nil {
		log.Fatalf("MySQL初始化失败: %v", err)
	}
	defer database.CloseMySQL()

	// 初始化Redis连接
	if err := database.InitRedis(); err != nil {
		log.Fatalf("Redis初始化失败: %v", err)
	}
	defer database.CloseRedis()

	// 创建WebSocket Hub
	hub := ws.NewHub()
	go hub.Run()

	// 设置路由
	r := routes.SetupRoutes(hub)

	// 启动服务器
	host := config.GlobalConfig.Server.Host
	port := config.GlobalConfig.Server.Port
	address := fmt.Sprintf("%s:%d", host, port)

	log.Printf("服务器启动在地址: %s", address)
	log.Printf("Swagger文档地址: http://%s:%d/swagger/index.html", host, port)
	log.Printf("WebSocket连接地址: ws://%s:%d/ws", host, port)

	// 优雅关闭
	go func() {
		if err := r.Run(address); err != nil {
			log.Fatalf("服务器启动失败: %v", err)
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Println("服务器正在关闭...")
	log.Println("服务器已关闭")
}
