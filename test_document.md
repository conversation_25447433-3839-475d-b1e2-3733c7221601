# 测试文档

这是一个用于测试的 Markdown 文件。

## 测试内容

### 1. 文本格式测试

**粗体文本**  
*斜体文本*  
~~删除线文本~~  
`行内代码`

### 2. 列表测试

#### 无序列表
- 项目 1
- 项目 2
  - 子项目 2.1
  - 子项目 2.2
- 项目 3

#### 有序列表
1. 第一步
2. 第二步
3. 第三步

### 3. 代码块测试

```javascript
function testFunction() {
    console.log("这是一个测试函数");
    return "测试成功";
}
```

```python
def test_function():
    print("这是一个Python测试函数")
    return "测试成功"
```

### 4. 表格测试

| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 数据1 | 数据2 | 数据3 |
| 测试A | 测试B | 测试C |
| 示例X | 示例Y | 示例Z |

### 5. 链接测试

- [外部链接](https://www.example.com)
- [内部链接](#测试内容)

### 6. 引用测试

> 这是一个引用块。
> 
> 可以包含多行内容。
> 
> > 这是嵌套引用。

### 7. 任务列表测试

- [x] 已完成的任务
- [ ] 未完成的任务
- [x] 另一个已完成的任务
- [ ] 待办事项

### 8. 数学公式测试

行内公式：$E = mc^2$

块级公式：
$$
\sum_{i=1}^{n} x_i = x_1 + x_2 + \cdots + x_n
$$

### 9. 图片测试

![测试图片](https://via.placeholder.com/300x200?text=Test+Image)

### 10. 分隔线测试

---

## 测试结果

✅ 文本格式正常  
✅ 列表显示正常  
✅ 代码块高亮正常  
✅ 表格对齐正常  
✅ 链接功能正常  

## 备注

这个文档包含了各种 Markdown 语法的测试用例，可以用来验证 Markdown 渲染器的功能是否正常。
