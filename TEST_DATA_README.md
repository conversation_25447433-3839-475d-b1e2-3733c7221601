# 百家乐测试数据使用说明

## 概述

本目录包含了用于验证百家乐计算逻辑的完整测试数据和工具。

## 文件说明

### 1. 测试工具
- **`generate_test_data.html`** - 交互式测试数据生成器
- **`test_result_calculation.html`** - 手动测试页面

### 2. 数据库脚本
- **`insert_test_data.sql`** - 插入预设测试数据
- **`verify_test_results.sql`** - 验证计算结果的正确性
- **`update_odds.sql`** - 更新赔率配置

### 3. 说明文档
- **`calculation_fix_explanation.md`** - 详细的修复说明
- **`TEST_DATA_README.md`** - 本文件

## 测试用例说明

### 测试用例1：庄赢（客户投注庄）
- **投注**: 庄1000
- **结果**: ["庄"]
- **预期**: 客户赢950，输口0，洗码量0

### 测试用例2：闲赢（客户投注闲）
- **投注**: 闲1000
- **结果**: ["闲"]
- **预期**: 客户赢1000，输口0，洗码量0

### 测试用例3：庄赢（客户投注闲）
- **投注**: 闲1000
- **结果**: ["庄"]
- **预期**: 客户输1000，输口1000，洗码量1000

### 测试用例4：和赢（和的投注本金变小费）
- **投注**: 和1000
- **结果**: ["和"]
- **预期**: 客户赢8000，输口0，洗码量0，小费1000

### 测试用例5：庄对赢（和底变小费）
- **投注**: 庄1000，庄对100，和底50
- **结果**: ["庄", "庄对"]
- **预期**: 客户赢2050，输口0，洗码量0，小费50

### 测试用例6：复合投注（客户输钱）
- **投注**: 庄500，闲800，和200，庄对100，闲对100，和底30
- **结果**: ["庄"]
- **预期**: 客户输725，输口725，洗码量725

## 使用方法

### 方法1：使用交互式测试工具

1. **启动服务**
   ```bash
   go run main.go
   ```

2. **打开测试页面**
   ```bash
   open generate_test_data.html
   ```

3. **连接并测试**
   - 点击"连接"按钮
   - 点击"登录"按钮
   - 点击"运行所有测试"或单独运行测试用例

### 方法2：使用数据库脚本

1. **更新赔率配置**
   ```sql
   source update_odds.sql;
   ```

2. **插入测试数据**
   ```sql
   source insert_test_data.sql;
   ```

3. **验证结果**
   ```sql
   source verify_test_results.sql;
   ```

### 方法3：手动测试

1. **打开手动测试页面**
   ```bash
   open test_result_calculation.html
   ```

2. **按照测试用例逐一测试**
   - 输入对应的投注金额
   - 选择对应的开奖结果
   - 检查计算结果

## 验证标准

### 计算公式
1. **总投注金额** = 所有投注项的金额总和
2. **总赢得金额** = 中奖投注项的（本金 + 赔付）
3. **净输赢** = 总赢得金额 - 总投注金额
4. **输口** = 如果净输赢 < 0，则输口 = -净输赢；否则输口 = 0
5. **洗码量** = 输口（业务规则：洗码量都是单边戏码）
6. **洗码费** = 洗码量 × 洗码率

### 小费规则
- **和结果**：和的投注本金变为小费
- **庄对/闲对结果**：和底（通用底注）本金变为小费

### 赔率配置
- **庄**: 0.95（净赔率，扣除5%佣金）
- **闲**: 1.0（净赔率，1:1）
- **和**: 8.0（净赔率，1:8）
- **庄对/闲对**: 11.0（净赔率，1:11）

## 常见问题

### Q: 为什么客户赢钱时洗码量是0？
A: 按照业务规则，洗码量=输口，客户赢钱时输口为0，所以洗码量也是0。

### Q: 小费是如何计算的？
A: 
- 开和时：和的投注本金变为小费
- 开庄对/闲对时：和底本金变为小费
- 不会重复计算

### Q: 如何验证计算结果？
A: 运行 `verify_test_results.sql` 脚本，会自动对比实际结果与预期结果。

## 调试建议

1. **检查日志**: 服务端会输出详细的计算过程日志
2. **对比预期**: 使用验证脚本检查计算结果
3. **单步测试**: 使用手动测试页面逐步验证
4. **数据库查询**: 直接查询bet_records表检查数据

## 注意事项

1. **测试前准备**: 确保数据库中的赔率配置正确
2. **数据清理**: 测试数据的wash_code都以"TEST"开头，便于清理
3. **并发测试**: 避免同时运行多个测试，可能导致数据混乱
4. **结果验证**: 每次修改计算逻辑后都应该重新运行所有测试用例
