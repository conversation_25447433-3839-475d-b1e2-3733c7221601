-- 验证百家乐计算结果的SQL脚本
-- 用于检查测试数据的计算结果是否符合预期

-- 创建临时表存储预期结果
CREATE TEMPORARY TABLE expected_results (
    hand_no INT,
    wash_code VARCHAR(50),
    expected_win_loss DECIMAL(12,2),
    expected_loss DECIMAL(12,2),
    expected_wash_amount DECIMAL(12,2),
    expected_amount_tip DECIMAL(12,2),
    description VARCHAR(200)
);

-- 插入预期结果
INSERT INTO expected_results VALUES
(1, 'TEST001', 950.00, 0.00, 0.00, 0.00, '庄赢-客户投注庄'),
(2, 'TEST002', 1000.00, 0.00, 0.00, 0.00, '闲赢-客户投注闲'),
(3, 'TEST003', -1000.00, 1000.00, 1000.00, 0.00, '庄赢-客户投注闲'),
(4, 'TEST004', 8000.00, 0.00, 0.00, 1000.00, '和赢-和投注本金变小费'),
(5, 'TEST005', 2050.00, 0.00, 0.00, 50.00, '庄对赢-和底变小费'),
(6, 'TEST006', -725.00, 725.00, 725.00, 0.00, '复合投注-客户输钱'),
(7, 'TEST007', 2230.00, 0.00, 0.00, 30.00, '闲对赢-和底变小费'),
(8, 'TEST008', -1530.00, 1530.00, 1530.00, 200.00, '和赢但其他全输-和投注本金变小费');

-- 对比实际结果与预期结果
SELECT 
    e.hand_no,
    e.wash_code,
    e.description,
    '输赢对比' as check_type,
    e.expected_win_loss as expected,
    COALESCE(b.win_loss, 0) as actual,
    CASE 
        WHEN ABS(e.expected_win_loss - COALESCE(b.win_loss, 0)) < 0.01 THEN '✓ 正确'
        ELSE '✗ 错误'
    END as result
FROM expected_results e
LEFT JOIN bet_records b ON e.hand_no = b.hand_no AND e.wash_code = b.wash_code

UNION ALL

SELECT 
    e.hand_no,
    e.wash_code,
    e.description,
    '输口对比' as check_type,
    e.expected_loss as expected,
    COALESCE(b.loss, 0) as actual,
    CASE 
        WHEN ABS(e.expected_loss - COALESCE(b.loss, 0)) < 0.01 THEN '✓ 正确'
        ELSE '✗ 错误'
    END as result
FROM expected_results e
LEFT JOIN bet_records b ON e.hand_no = b.hand_no AND e.wash_code = b.wash_code

UNION ALL

SELECT 
    e.hand_no,
    e.wash_code,
    e.description,
    '洗码量对比' as check_type,
    e.expected_wash_amount as expected,
    COALESCE(b.wash_amount, 0) as actual,
    CASE 
        WHEN ABS(e.expected_wash_amount - COALESCE(b.wash_amount, 0)) < 0.01 THEN '✓ 正确'
        ELSE '✗ 错误'
    END as result
FROM expected_results e
LEFT JOIN bet_records b ON e.hand_no = b.hand_no AND e.wash_code = b.wash_code

UNION ALL

SELECT 
    e.hand_no,
    e.wash_code,
    e.description,
    '小费对比' as check_type,
    e.expected_amount_tip as expected,
    COALESCE(b.amount_tip, 0) as actual,
    CASE 
        WHEN ABS(e.expected_amount_tip - COALESCE(b.amount_tip, 0)) < 0.01 THEN '✓ 正确'
        ELSE '✗ 错误'
    END as result
FROM expected_results e
LEFT JOIN bet_records b ON e.hand_no = b.hand_no AND e.wash_code = b.wash_code

ORDER BY hand_no, check_type;

-- 汇总验证结果
SELECT 
    '验证汇总' as summary,
    COUNT(*) as total_checks,
    SUM(CASE WHEN result LIKE '%正确%' THEN 1 ELSE 0 END) as passed,
    SUM(CASE WHEN result LIKE '%错误%' THEN 1 ELSE 0 END) as failed,
    CONCAT(
        ROUND(SUM(CASE WHEN result LIKE '%正确%' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2), 
        '%'
    ) as pass_rate
FROM (
    SELECT 
        CASE 
            WHEN ABS(e.expected_win_loss - COALESCE(b.win_loss, 0)) < 0.01 THEN '✓ 正确'
            ELSE '✗ 错误'
        END as result
    FROM expected_results e
    LEFT JOIN bet_records b ON e.hand_no = b.hand_no AND e.wash_code = b.wash_code
    
    UNION ALL
    
    SELECT 
        CASE 
            WHEN ABS(e.expected_loss - COALESCE(b.loss, 0)) < 0.01 THEN '✓ 正确'
            ELSE '✗ 错误'
        END as result
    FROM expected_results e
    LEFT JOIN bet_records b ON e.hand_no = b.hand_no AND e.wash_code = b.wash_code
    
    UNION ALL
    
    SELECT 
        CASE 
            WHEN ABS(e.expected_wash_amount - COALESCE(b.wash_amount, 0)) < 0.01 THEN '✓ 正确'
            ELSE '✗ 错误'
        END as result
    FROM expected_results e
    LEFT JOIN bet_records b ON e.hand_no = b.hand_no AND e.wash_code = b.wash_code
    
    UNION ALL
    
    SELECT 
        CASE 
            WHEN ABS(e.expected_amount_tip - COALESCE(b.amount_tip, 0)) < 0.01 THEN '✓ 正确'
            ELSE '✗ 错误'
        END as result
    FROM expected_results e
    LEFT JOIN bet_records b ON e.hand_no = b.hand_no AND e.wash_code = b.wash_code
) validation_results;

-- 显示详细的计算过程（用于调试）
SELECT 
    b.hand_no,
    b.wash_code,
    b.user_name,
    CONCAT('庄:', b.banker_amount, ' 闲:', b.player_amount, ' 和:', b.tie_amount) as main_bets,
    CONCAT('庄对:', b.banker_pair_amount, ' 闲对:', b.player_pair_amount, ' 和底:', b.amount_bottom) as side_bets,
    b.win_result,
    (b.banker_amount + b.player_amount + b.tie_amount + b.banker_pair_amount + b.player_pair_amount) as total_bet,
    b.win_loss,
    b.loss,
    b.wash_amount,
    b.amount_tip,
    b.wash_tip,
    CASE 
        WHEN b.win_loss > 0 THEN CONCAT('客户赢 ', b.win_loss)
        WHEN b.win_loss < 0 THEN CONCAT('客户输 ', ABS(b.win_loss))
        ELSE '平局'
    END as result_summary
FROM bet_records b
WHERE b.wash_code LIKE 'TEST%'
ORDER BY b.hand_no;

-- 清理临时表
DROP TEMPORARY TABLE expected_results;
