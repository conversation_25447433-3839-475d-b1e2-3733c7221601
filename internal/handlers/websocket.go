package handlers

import (
	"log"
	"net/http"

	"accounting_enter/internal/config"
	ws "accounting_enter/internal/websocket"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

var upgrader = websocket.Upgrader{
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
	CheckOrigin: func(r *http.Request) bool {
		return config.GlobalConfig.WebSocket.CheckOrigin
	},
}

// WebSocketHandler 处理WebSocket连接
// @Summary WebSocket连接端点
// @Description 建立WebSocket连接用于实时通信
// @Tags WebSocket
// @Accept json
// @Produce json
// @Success 101 {string} string "Switching Protocols"
// @Router /ws [get]
func WebSocketHandler(hub *ws.Hub) gin.HandlerFunc {
	return func(c *gin.Context) {
		conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
		if err != nil {
			log.Printf("WebSocket升级失败: %v", err)
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "WebSocket升级失败",
			})
			return
		}

		client := ws.NewClient(conn)
		hub.Register <- client

		// 启动客户端的读写协程
		go client.WritePump()
		go client.ReadPump(hub)
	}
}

// GetWebSocketStats 获取WebSocket统计信息
// @Summary 获取WebSocket统计信息
// @Description 获取当前WebSocket连接数量和客户端列表
// @Tags WebSocket
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "统计信息"
// @Router /api/ws/stats [get]
func GetWebSocketStats(hub *ws.Hub) gin.HandlerFunc {
	return func(c *gin.Context) {
		stats := map[string]interface{}{
			"client_count": hub.GetClientCount(),
			"clients":      hub.GetClientList(),
		}
		c.JSON(http.StatusOK, gin.H{
			"code": 200,
			"data": stats,
			"msg":  "获取统计信息成功",
		})
	}
}

// BroadcastMessage 广播消息到所有客户端
// @Summary 广播消息
// @Description 向所有WebSocket客户端广播消息
// @Tags WebSocket
// @Accept json
// @Produce json
// @Param message body map[string]interface{} true "消息内容"
// @Success 200 {object} map[string]interface{} "广播结果"
// @Router /api/ws/broadcast [post]
func BroadcastMessage(hub *ws.Hub) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req map[string]interface{}
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"code": 400,
				"msg":  "请求参数错误",
				"data": nil,
			})
			return
		}

		message := ws.Message{
			Type: "broadcast",
			From: "server",
			Data: req,
		}

		hub.BroadcastToAll(message)

		c.JSON(http.StatusOK, gin.H{
			"code": 200,
			"msg":  "消息广播成功",
			"data": nil,
		})
	}
}
