package websocket

import (
	"encoding/json"
	"fmt"
	"log"
	"net"
	"strconv"
	"strings"
	"time"

	"accounting_enter/internal/services"

	"github.com/gorilla/websocket"
)

const (
	// 写入等待时间
	writeWait = 30 * time.Second

	// 读取等待时间
	pongWait = 120 * time.Second

	// ping周期，必须小于pongWait
	pingPeriod = (pongWait * 9) / 10

	// 最大消息大小 - 增加以支持批量数据
	maxMessageSize = 1024 * 1024 // 1MB
)

// ReadPump 处理客户端的读取消息
func (c *Client) ReadPump(hub *Hub) {
	defer func() {
		log.Printf("客户端 %s 断开连接", c.ID)
		hub.Unregister <- c
		c.Conn.Close()
	}()

	c.Conn.SetReadLimit(maxMessageSize)
	c.Conn.SetReadDeadline(time.Now().Add(pongWait))
	c.Conn.SetPongHandler(func(string) error {
		c.Conn.SetReadDeadline(time.Now().Add(pongWait))
		return nil
	})

	for {
		_, message, err := c.Conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("客户端 %s WebSocket读取错误: %v", c.ID, err)
			} else {
				log.Printf("客户端 %s 正常断开: %v", c.ID, err)
			}
			break
		}

		// 记录接收到的消息大小
		messageSize := len(message)
		if messageSize > 1024*1024 { // 大于1MB的消息
			log.Printf("客户端 %s 接收到大消息: %d bytes", c.ID, messageSize)
		}

		// 解析消息
		var msg Message
		if err := json.Unmarshal(message, &msg); err != nil {
			log.Printf("客户端 %s 消息解析错误: %v, 消息内容: %s", c.ID, err, string(message[:min(len(message), 200)]))
			continue
		}

		// 设置消息发送者
		msg.From = c.ID
		msg.Timestamp = getCurrentTimestamp()

		// 处理消息
		c.handleMessage(hub, msg)
	}
}

// min 返回两个整数中的较小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// WritePump 处理客户端的写入消息
func (c *Client) WritePump() {
	ticker := time.NewTicker(pingPeriod)
	defer func() {
		ticker.Stop()
		log.Printf("客户端 %s WritePump结束", c.ID)
		c.Conn.Close()
	}()

	for {
		select {
		case message, ok := <-c.Send:
			c.Conn.SetWriteDeadline(time.Now().Add(writeWait))
			if !ok {
				log.Printf("客户端 %s 发送通道已关闭", c.ID)
				c.Conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			// 记录发送的消息大小
			messageSize := len(message)
			if messageSize > 1024*1024 { // 大于1MB的消息
				log.Printf("客户端 %s 发送大消息: %d bytes", c.ID, messageSize)
			}

			w, err := c.Conn.NextWriter(websocket.TextMessage)
			if err != nil {
				log.Printf("客户端 %s 创建写入器失败: %v", c.ID, err)
				return
			}

			if _, err := w.Write(message); err != nil {
				log.Printf("客户端 %s 写入消息失败: %v", c.ID, err)
				return
			}

			// 添加队列中的消息到当前WebSocket消息
			n := len(c.Send)
			for i := 0; i < n; i++ {
				w.Write([]byte{'\n'})
				w.Write(<-c.Send)
			}

			if err := w.Close(); err != nil {
				log.Printf("客户端 %s 关闭写入器失败: %v", c.ID, err)
				return
			}

		case <-ticker.C:
			c.Conn.SetWriteDeadline(time.Now().Add(writeWait))
			if err := c.Conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				log.Printf("客户端 %s 发送ping失败: %v", c.ID, err)
				return
			}
		}
	}
}

// getClientIP 获取客户端IP地址
func (c *Client) getClientIP() string {
	// 从WebSocket连接中获取远程地址
	remoteAddr := c.Conn.RemoteAddr().String()

	// 解析IP地址，去掉端口号
	if host, _, err := net.SplitHostPort(remoteAddr); err == nil {
		return host
	}

	// 如果解析失败，直接返回原始地址
	return strings.Split(remoteAddr, ":")[0]
}

// handleMessage 处理接收到的消息
func (c *Client) handleMessage(hub *Hub, msg Message) {
	switch msg.Type {
	case "ping":
		// 处理ping消息
		pongMsg := Message{
			Type:      "pong",
			From:      "server",
			To:        c.ID,
			Data:      "pong",
			Timestamp: getCurrentTimestamp(),
		}
		if data, err := json.Marshal(pongMsg); err == nil {
			select {
			case c.Send <- data:
			default:
				close(c.Send)
			}
		}

	case "login":
		// 用户登录
		c.handleLogin(hub, msg)

	case "logout":
		// 用户登出
		c.handleLogout()

	case "get_user_info":
		// 获取用户信息
		c.handleGetUserInfo()

	case "change_password":
		// 修改密码
		c.handleChangePassword(msg)

	case "verify_token":
		// 验证令牌
		c.handleVerifyToken(msg)

	case "get_table_info":
		// 获取桌台信息
		c.handleGetTableInfo()

	case "get_user_by_wash_code":
		// 通过洗码号获取客户信息
		c.handleGetUserByWashCode(msg)

	case "apply_out_code":
		// 申请出码
		c.handleApplyOutCode(msg)

	case "get_out_code_list":
		// 获取出码申请列表
		c.handleGetOutCodeList(msg)

	case "get_out_code_detail":
		// 获取出码申请详情
		c.handleGetOutCodeDetail(msg)

	case "start_shuffle":
		// 洗牌开场
		c.handleStartShuffle(msg)

	case "get_sys_user_by_serial":
		// 通过员工编号获取系统用户信息
		c.handleGetSysUserBySerial(msg)

	case "broadcast":
		// 广播消息
		broadcastMsg := Message{
			Type:      "broadcast",
			From:      c.ID,
			Data:      msg.Data,
			Timestamp: getCurrentTimestamp(),
		}
		hub.BroadcastToAll(broadcastMsg)

	case "private":
		// 私聊消息
		if msg.To != "" {
			privateMsg := Message{
				Type:      "private",
				From:      c.ID,
				To:        msg.To,
				Data:      msg.Data,
				Timestamp: getCurrentTimestamp(),
			}
			hub.SendToClient(msg.To, privateMsg)
		}

	case "get_clients":
		// 获取客户端列表
		clients := hub.GetClientList()
		clientsMsg := Message{
			Type:      "clients_list",
			From:      "server",
			To:        c.ID,
			Data:      clients,
			Timestamp: getCurrentTimestamp(),
		}
		if data, err := json.Marshal(clientsMsg); err == nil {
			select {
			case c.Send <- data:
			default:
				close(c.Send)
			}
		}

	case "batch_bet_entry":
		// 处理批量下注录入请求
		c.handleBatchBetEntry(msg)

	case "enter_result":
		c.handleEnterResult(msg)

	case "get_code_summary":
		c.handleGetCodeSummary(msg)

	case "batch_settlement":
		c.handleBatchSettlement(msg)

	case "close_table":
		c.handleCloseTable(msg)

	case "get_latest_tables_start":
		// 通过客户端IP查询最新开台数据（无需传参）
		c.handleGetLatestTablesStart()

	case "get_hand_records_result":
		// 获取条口记录结果（用于露珠图）
		c.handleGetHandRecordsResult(msg)

	case "get_bet_records_list":
		// 获取下注记录列表
		c.handleGetBetRecordsList(msg)

	case "get_shuffle_records_list":
		// 获取洗牌记录列表
		c.handleGetShuffleRecordsList(msg)

	case "get_settlement_display":
		// 获取收盘展示数据
		c.handleGetSettlementDisplay(msg)

	case "query_bet_records":
		// 查询Redis中的下注记录
		c.handleQueryBetRecords(msg)

	case "get_bet_limits":
		// 获取桌台下注限制信息
		c.handleGetBetLimits(msg)

	case "check_bet_limits":
		// 实时检查下注是否超限
		c.handleCheckBetLimits(msg)

	case "get_table_stats":
		// 获取桌台情况统计
		c.handleGetTableStats(msg)

	default:
		log.Printf("未知消息类型: %s", msg.Type)
	}
}

// handleLogin 处理用户登录
func (c *Client) handleLogin(hub *Hub, msg Message) {
	authService := services.NewAuthService()
	clientIP := c.getClientIP()

	// 解析登录请求数据
	var loginReq services.LoginRequest
	if data, ok := msg.Data.(map[string]interface{}); ok {
		if username, exists := data["username"].(string); exists {
			loginReq.Username = username
		}
		if password, exists := data["password"].(string); exists {
			loginReq.Password = password
		}
	}

	// 执行登录
	loginResp, err := authService.Login(loginReq, clientIP)

	var responseMsg Message
	if err != nil {
		responseMsg = Message{
			Type: "login_error",
			From: "server",
			To:   c.ID,
			Data: map[string]interface{}{
				"error": err.Error(),
			},
			Timestamp: getCurrentTimestamp(),
		}
	} else {
		// 登录成功，更新客户端信息
		c.UserID = fmt.Sprintf("%d", loginResp.UserInfo.ID)
		c.Username = loginResp.UserInfo.Username
		c.Token = loginResp.Token
		c.IsAuthenticated = true

		// 通知Hub更新客户端信息
		hub.UpdateClientAuth(c)

		responseMsg = Message{
			Type: "login_success",
			From: "server",
			To:   c.ID,
			Data: map[string]interface{}{
				"token":     loginResp.Token,
				"user_info": loginResp.UserInfo,
				"message":   "登录成功",
			},
			Timestamp: getCurrentTimestamp(),
		}
	}

	// 发送响应
	if data, err := json.Marshal(responseMsg); err == nil {
		select {
		case c.Send <- data:
		default:
			close(c.Send)
		}
	}
}

// handleLogout 处理用户登出
func (c *Client) handleLogout() {
	var userID int
	var username string

	// 如果用户已认证，获取用户信息用于记录日志
	if c.IsAuthenticated && c.Token != "" {
		authService := services.NewAuthService()
		if claims, err := authService.VerifyToken(c.Token); err == nil {
			userID = claims.UserID
			username = claims.Username
		}
	}

	// 如果没有从令牌获取到用户信息，使用客户端存储的信息
	if username == "" {
		username = c.Username
	}

	// 记录登出日志
	if username != "" {
		authService := services.NewAuthService()
		clientIP := c.getClientIP()
		err := authService.Logout(userID, username, clientIP)
		if err != nil {
			fmt.Printf("记录登出日志失败: %v\n", err)
		}
	}

	// 清除客户端认证信息
	c.UserID = ""
	c.Username = ""
	c.Token = ""
	c.IsAuthenticated = false

	responseMsg := Message{
		Type: "logout_success",
		From: "server",
		To:   c.ID,
		Data: map[string]interface{}{
			"message": "登出成功",
		},
		Timestamp: getCurrentTimestamp(),
	}

	// 发送响应
	if data, err := json.Marshal(responseMsg); err == nil {
		select {
		case c.Send <- data:
		default:
			close(c.Send)
		}
	}
}

// handleGetUserInfo 处理获取用户信息请求
func (c *Client) handleGetUserInfo() {
	var responseMsg Message

	if !c.IsAuthenticated {
		responseMsg = Message{
			Type: "user_info_error",
			From: "server",
			To:   c.ID,
			Data: map[string]interface{}{
				"error": "用户未登录",
			},
			Timestamp: getCurrentTimestamp(),
		}
	} else {
		authService := services.NewAuthService()

		// 从令牌中获取用户ID
		claims, err := authService.VerifyToken(c.Token)
		if err != nil {
			responseMsg = Message{
				Type: "user_info_error",
				From: "server",
				To:   c.ID,
				Data: map[string]interface{}{
					"error": "令牌无效，请重新登录",
				},
				Timestamp: getCurrentTimestamp(),
			}
		} else {
			// 获取用户详细信息
			user, err := authService.GetUserByID(claims.UserID)
			if err != nil {
				responseMsg = Message{
					Type: "user_info_error",
					From: "server",
					To:   c.ID,
					Data: map[string]interface{}{
						"error": err.Error(),
					},
					Timestamp: getCurrentTimestamp(),
				}
			} else {
				userInfo := authService.FormatUserInfo(user, c.getClientIP(), uint(time.Now().Unix()))
				responseMsg = Message{
					Type: "user_info_success",
					From: "server",
					To:   c.ID,
					Data: map[string]interface{}{
						"user_info": userInfo,
					},
					Timestamp: getCurrentTimestamp(),
				}
			}
		}
	}

	// 发送响应
	if data, err := json.Marshal(responseMsg); err == nil {
		select {
		case c.Send <- data:
		default:
			close(c.Send)
		}
	}
}

// handleChangePassword 处理修改密码请求
func (c *Client) handleChangePassword(msg Message) {
	var responseMsg Message

	if !c.IsAuthenticated {
		responseMsg = Message{
			Type: "change_password_error",
			From: "server",
			To:   c.ID,
			Data: map[string]interface{}{
				"error": "用户未登录",
			},
			Timestamp: getCurrentTimestamp(),
		}
	} else {
		authService := services.NewAuthService()

		// 验证令牌
		claims, err := authService.VerifyToken(c.Token)
		if err != nil {
			responseMsg = Message{
				Type: "change_password_error",
				From: "server",
				To:   c.ID,
				Data: map[string]interface{}{
					"error": "令牌无效，请重新登录",
				},
				Timestamp: getCurrentTimestamp(),
			}
		} else {
			// 解析修改密码请求数据
			var oldPassword, newPassword string
			if data, ok := msg.Data.(map[string]interface{}); ok {
				if old, exists := data["old_password"].(string); exists {
					oldPassword = old
				}
				if new, exists := data["new_password"].(string); exists {
					newPassword = new
				}
			}

			// 执行密码修改
			err = authService.ChangePassword(claims.UserID, oldPassword, newPassword)
			if err != nil {
				responseMsg = Message{
					Type: "change_password_error",
					From: "server",
					To:   c.ID,
					Data: map[string]interface{}{
						"error": err.Error(),
					},
					Timestamp: getCurrentTimestamp(),
				}
			} else {
				responseMsg = Message{
					Type: "change_password_success",
					From: "server",
					To:   c.ID,
					Data: map[string]interface{}{
						"message": "密码修改成功",
					},
					Timestamp: getCurrentTimestamp(),
				}
			}
		}
	}

	// 发送响应
	if data, err := json.Marshal(responseMsg); err == nil {
		select {
		case c.Send <- data:
		default:
			close(c.Send)
		}
	}
}

// handleVerifyToken 处理验证令牌请求
func (c *Client) handleVerifyToken(msg Message) {
	authService := services.NewAuthService()

	// 解析令牌
	var tokenString string
	if data, ok := msg.Data.(map[string]interface{}); ok {
		if token, exists := data["token"].(string); exists {
			tokenString = token
		}
	}

	// 验证令牌
	claims, err := authService.VerifyToken(tokenString)

	var responseMsg Message
	if err != nil {
		responseMsg = Message{
			Type: "verify_token_error",
			From: "server",
			To:   c.ID,
			Data: map[string]interface{}{
				"error": err.Error(),
				"valid": false,
			},
			Timestamp: getCurrentTimestamp(),
		}
	} else {
		responseMsg = Message{
			Type: "verify_token_success",
			From: "server",
			To:   c.ID,
			Data: map[string]interface{}{
				"valid":    true,
				"user_id":  claims.UserID,
				"username": claims.Username,
				"realname": claims.Realname,
				"expires":  claims.ExpiresAt.Unix(),
			},
			Timestamp: getCurrentTimestamp(),
		}
	}

	// 发送响应
	if data, err := json.Marshal(responseMsg); err == nil {
		select {
		case c.Send <- data:
		default:
			close(c.Send)
		}
	}
}

// handleGetTableInfo 处理获取桌台信息请求（使用客户端IP）
func (c *Client) handleGetTableInfo() {
	tableService := services.NewTableService()
	clientIP := c.getClientIP()

	// 获取桌台信息及其下注配置
	table, bets, err := tableService.GetTableWithBets(clientIP)

	var responseMsg Message
	if err != nil {
		responseMsg = Message{
			Type: "table_info_error",
			From: "server",
			To:   c.ID,
			Data: map[string]interface{}{
				"error":     err.Error(),
				"client_ip": clientIP,
			},
			Timestamp: getCurrentTimestamp(),
		}
	} else {
		// 格式化响应数据
		tableResponse := tableService.FormatTableResponse(table, bets)
		responseMsg = Message{
			Type: "table_info_success",
			From: "server",
			To:   c.ID,
			Data: map[string]interface{}{
				"table":     tableResponse,
				"client_ip": clientIP,
			},
			Timestamp: getCurrentTimestamp(),
		}
	}

	// 发送响应
	if data, err := json.Marshal(responseMsg); err == nil {
		select {
		case c.Send <- data:
		default:
			close(c.Send)
		}
	}
}

// handleGetUserByWashCode 处理通过洗码号获取客户信息请求
func (c *Client) handleGetUserByWashCode(msg Message) {
	var responseMsg Message

	// 解析请求数据
	var washCode string
	if data, ok := msg.Data.(map[string]interface{}); ok {
		if code, exists := data["wash_code"].(string); exists {
			washCode = code
		}
	}

	if washCode == "" {
		responseMsg = Message{
			Type: "get_user_by_wash_code_error",
			From: "server",
			To:   c.ID,
			Data: map[string]interface{}{
				"error": "洗码号不能为空",
			},
			Timestamp: getCurrentTimestamp(),
		}
	} else {
		// 获取客户信息
		userService := services.NewUserService()
		userInfo, err := userService.GetUserByWashCode(washCode)

		if err != nil {
			responseMsg = Message{
				Type: "get_user_by_wash_code_error",
				From: "server",
				To:   c.ID,
				Data: map[string]interface{}{
					"error":     err.Error(),
					"wash_code": washCode,
				},
				Timestamp: getCurrentTimestamp(),
			}
		} else {
			responseMsg = Message{
				Type: "get_user_by_wash_code_success",
				From: "server",
				To:   c.ID,
				Data: map[string]interface{}{
					"user_info": userInfo,
					"wash_code": washCode,
				},
				Timestamp: getCurrentTimestamp(),
			}
		}
	}

	// 发送响应
	if data, err := json.Marshal(responseMsg); err == nil {
		select {
		case c.Send <- data:
		default:
			close(c.Send)
		}
	}
}

// handleApplyOutCode 处理申请出码请求
func (c *Client) handleApplyOutCode(msg Message) {
	var responseMsg Message

	// 检查用户是否已登录
	if !c.IsAuthenticated || c.Token == "" {
		responseMsg = Message{
			Type: "apply_out_code_error",
			From: "server",
			To:   c.ID,
			Data: map[string]interface{}{
				"error": "用户未登录",
			},
			Timestamp: getCurrentTimestamp(),
		}
	} else {
		// 验证令牌并获取用户信息
		authService := services.NewAuthService()
		claims, err := authService.VerifyToken(c.Token)
		if err != nil {
			responseMsg = Message{
				Type: "apply_out_code_error",
				From: "server",
				To:   c.ID,
				Data: map[string]interface{}{
					"error": "令牌无效，请重新登录",
				},
				Timestamp: getCurrentTimestamp(),
			}
		} else {
			// 解析申请数据
			var req services.OutCodeRequest
			if data, ok := msg.Data.(map[string]interface{}); ok {
				if accountPeriod, exists := data["account_period"].(string); exists {
					req.AccountPeriod = accountPeriod
				}
				if operationType, exists := data["operation_type"].(float64); exists {
					req.OperationType = int8(operationType)
				}
				if amounts, exists := data["amounts"].([]interface{}); exists {
					for _, amountData := range amounts {
						if amountMap, ok := amountData.(map[string]interface{}); ok {
							var amount services.OutCodeAmountRequest
							if currencyType, exists := amountMap["currency_type"].(float64); exists {
								amount.CurrencyType = int8(currencyType)
							}
							if totalAmount, exists := amountMap["total_amount"].(float64); exists {
								amount.TotalAmount = totalAmount
							}
							req.Amounts = append(req.Amounts, amount)
						}
					}
				}
			}

			// 获取客户端IP
			clientIP := c.getClientIP()

			// 执行批量申请出码
			financeService := services.NewFinanceService()
			results, err := financeService.BatchApplyOutCode(req, claims.Realname, clientIP)

			if err != nil {
				responseMsg = Message{
					Type: "apply_out_code_error",
					From: "server",
					To:   c.ID,
					Data: map[string]interface{}{
						"error": err.Error(),
					},
					Timestamp: getCurrentTimestamp(),
				}
			} else {
				responseMsg = Message{
					Type: "apply_out_code_success",
					From: "server",
					To:   c.ID,
					Data: map[string]interface{}{
						"out_code_list": results,
						"message":       "出码申请提交成功",
					},
					Timestamp: getCurrentTimestamp(),
				}
			}
		}
	}

	// 发送响应
	if data, err := json.Marshal(responseMsg); err == nil {
		select {
		case c.Send <- data:
		default:
			close(c.Send)
		}
	}
}

// handleGetOutCodeList 处理获取出码申请列表请求
func (c *Client) handleGetOutCodeList(msg Message) {
	var responseMsg Message

	// 检查用户是否已登录
	if !c.IsAuthenticated || c.Token == "" {
		responseMsg = Message{
			Type: "get_out_code_list_error",
			From: "server",
			To:   c.ID,
			Data: map[string]interface{}{
				"error": "用户未登录",
			},
			Timestamp: getCurrentTimestamp(),
		}
	} else {
		// 验证令牌
		authService := services.NewAuthService()
		_, err := authService.VerifyToken(c.Token)
		if err != nil {
			responseMsg = Message{
				Type: "get_out_code_list_error",
				From: "server",
				To:   c.ID,
				Data: map[string]interface{}{
					"error": "令牌无效，请重新登录",
				},
				Timestamp: getCurrentTimestamp(),
			}
		} else {
			// 解析查询参数
			var tableID int
			var status int8
			var limit = 20
			var offset = 0

			if data, ok := msg.Data.(map[string]interface{}); ok {
				// table_id为可选参数，用于过滤特定桌台的出码申请，为0时查询所有桌台
				if tid, exists := data["table_id"].(float64); exists {
					tableID = int(tid)
				}
				if s, exists := data["status"].(float64); exists {
					status = int8(s)
				}
				if l, exists := data["limit"].(float64); exists {
					limit = int(l)
				}
				if o, exists := data["offset"].(float64); exists {
					offset = int(o)
				}
			}

			// 获取出码申请列表
			financeService := services.NewFinanceService()
			list, total, err := financeService.GetOutCodeList(tableID, status, limit, offset)

			if err != nil {
				responseMsg = Message{
					Type: "get_out_code_list_error",
					From: "server",
					To:   c.ID,
					Data: map[string]interface{}{
						"error": err.Error(),
					},
					Timestamp: getCurrentTimestamp(),
				}
			} else {
				responseMsg = Message{
					Type: "get_out_code_list_success",
					From: "server",
					To:   c.ID,
					Data: map[string]interface{}{
						"list":   list,
						"total":  total,
						"limit":  limit,
						"offset": offset,
					},
					Timestamp: getCurrentTimestamp(),
				}
			}
		}
	}

	// 发送响应
	if data, err := json.Marshal(responseMsg); err == nil {
		select {
		case c.Send <- data:
		default:
			close(c.Send)
		}
	}
}

// handleGetOutCodeDetail 处理获取出码申请详情请求
func (c *Client) handleGetOutCodeDetail(msg Message) {
	var responseMsg Message

	// 检查用户是否已登录
	if !c.IsAuthenticated || c.Token == "" {
		responseMsg = Message{
			Type: "get_out_code_detail_error",
			From: "server",
			To:   c.ID,
			Data: map[string]interface{}{
				"error": "用户未登录",
			},
			Timestamp: getCurrentTimestamp(),
		}
	} else {
		// 验证令牌
		authService := services.NewAuthService()
		_, err := authService.VerifyToken(c.Token)
		if err != nil {
			responseMsg = Message{
				Type: "get_out_code_detail_error",
				From: "server",
				To:   c.ID,
				Data: map[string]interface{}{
					"error": "令牌无效，请重新登录",
				},
				Timestamp: getCurrentTimestamp(),
			}
		} else {
			// 解析申请ID
			var id int
			if data, ok := msg.Data.(map[string]interface{}); ok {
				if applyID, exists := data["id"].(float64); exists {
					id = int(applyID)
				}
			}

			if id <= 0 {
				responseMsg = Message{
					Type: "get_out_code_detail_error",
					From: "server",
					To:   c.ID,
					Data: map[string]interface{}{
						"error": "申请ID不能为空",
					},
					Timestamp: getCurrentTimestamp(),
				}
			} else {
				// 获取出码申请详情
				financeService := services.NewFinanceService()
				detail, err := financeService.GetOutCodeByID(id)

				if err != nil {
					responseMsg = Message{
						Type: "get_out_code_detail_error",
						From: "server",
						To:   c.ID,
						Data: map[string]interface{}{
							"error": err.Error(),
							"id":    id,
						},
						Timestamp: getCurrentTimestamp(),
					}
				} else {
					responseMsg = Message{
						Type: "get_out_code_detail_success",
						From: "server",
						To:   c.ID,
						Data: map[string]interface{}{
							"out_code_info": detail,
						},
						Timestamp: getCurrentTimestamp(),
					}
				}
			}
		}
	}

	// 发送响应
	if data, err := json.Marshal(responseMsg); err == nil {
		select {
		case c.Send <- data:
		default:
			close(c.Send)
		}
	}
}

// handleStartShuffle 处理洗牌开场请求
func (c *Client) handleStartShuffle(msg Message) {
	var responseMsg Message

	// 检查用户是否已登录
	if !c.IsAuthenticated || c.Token == "" {
		responseMsg = Message{
			Type: "start_shuffle_error",
			From: "server",
			To:   c.ID,
			Data: map[string]interface{}{
				"error": "用户未登录",
			},
			Timestamp: getCurrentTimestamp(),
		}
	} else {
		// 验证令牌
		authService := services.NewAuthService()
		_, err := authService.VerifyToken(c.Token)
		if err != nil {
			responseMsg = Message{
				Type: "start_shuffle_error",
				From: "server",
				To:   c.ID,
				Data: map[string]interface{}{
					"error": "令牌无效，请重新登录",
				},
				Timestamp: getCurrentTimestamp(),
			}
		} else {
			// 解析洗牌开场请求数据
			var req services.ShuffleStartRequest
			if data, ok := msg.Data.(map[string]interface{}); ok {
				if accountPeriod, exists := data["account_period"].(string); exists {
					req.AccountPeriod = accountPeriod
				}
				if shift, exists := data["shift"].(float64); exists {
					req.Shift = int8(shift)
				}
				if shuffleMethod, exists := data["shuffle_method"].(string); exists {
					req.ShuffleMethod = shuffleMethod
				}
				if cardColor, exists := data["card_color"].(string); exists {
					req.CardColor = cardColor
				}
				if monitorID, exists := data["monitor_id"].(string); exists {
					req.MonitorID = monitorID
				}
				if monitorName, exists := data["monitor_name"].(string); exists {
					req.MonitorName = monitorName
				}
				if adminID, exists := data["admin_id"].(string); exists {
					req.AdminID = adminID
				}
				if adminName, exists := data["admin_name"].(string); exists {
					req.AdminName = adminName
				}
				if shuffleTablePoker, exists := data["shuffle_table_poker"].(string); exists {
					req.ShuffleTablePoker = shuffleTablePoker
				}
				if tablePoker, exists := data["table_poker"].(string); exists {
					req.TablePoker = tablePoker
				}
				if monitorPoker, exists := data["monitor_poker"].(string); exists {
					req.MonitorPoker = monitorPoker
				}
				if cutCardDealer, exists := data["cut_card_dealer"].(string); exists {
					req.CutCardDealer = cutCardDealer
				}
			}

			// 获取客户端IP
			clientIP := c.getClientIP()

			// 执行洗牌开场
			tableService := services.NewTableService()
			result, err := tableService.StartShuffle(req, clientIP)

			if err != nil {
				responseMsg = Message{
					Type: "start_shuffle_error",
					From: "server",
					To:   c.ID,
					Data: map[string]interface{}{
						"error": err.Error(),
					},
					Timestamp: getCurrentTimestamp(),
				}
			} else {
				responseMsg = Message{
					Type: "start_shuffle_success",
					From: "server",
					To:   c.ID,
					Data: map[string]interface{}{
						"shuffle_record": result.ShuffleRecord,
						"tables_start":   result.TablesStart,
						"message":        result.Message,
					},
					Timestamp: getCurrentTimestamp(),
				}
			}
		}
	}

	// 发送响应
	if data, err := json.Marshal(responseMsg); err == nil {
		select {
		case c.Send <- data:
		default:
			close(c.Send)
		}
	}
}

// handleGetSysUserBySerial 处理通过员工编号获取系统用户信息请求
func (c *Client) handleGetSysUserBySerial(msg Message) {
	var responseMsg Message

	// 检查用户是否已登录
	if !c.IsAuthenticated || c.Token == "" {
		responseMsg = Message{
			Type: "get_sys_user_by_serial_error",
			From: "server",
			To:   c.ID,
			Data: map[string]interface{}{
				"error": "用户未登录",
			},
			Timestamp: getCurrentTimestamp(),
		}
	} else {
		// 验证令牌
		authService := services.NewAuthService()
		_, err := authService.VerifyToken(c.Token)
		if err != nil {
			responseMsg = Message{
				Type: "get_sys_user_by_serial_error",
				From: "server",
				To:   c.ID,
				Data: map[string]interface{}{
					"error": "令牌无效，请重新登录",
				},
				Timestamp: getCurrentTimestamp(),
			}
		} else {
			// 解析员工编号
			var serialNumber string
			if data, ok := msg.Data.(map[string]interface{}); ok {
				if serial, exists := data["serial_number"]; exists {
					switch v := serial.(type) {
					case float64:
						serialNumber = fmt.Sprintf("%.0f", v)
					case string:
						serialNumber = v
					case int:
						serialNumber = strconv.Itoa(v)
					case int64:
						serialNumber = strconv.FormatInt(v, 10)
					default:
						serialNumber = fmt.Sprintf("%v", v)
					}
				}
			}

			if serialNumber == "" {
				responseMsg = Message{
					Type: "get_sys_user_by_serial_error",
					From: "server",
					To:   c.ID,
					Data: map[string]interface{}{
						"error": "员工编号不能为空或无效",
					},
					Timestamp: getCurrentTimestamp(),
				}
			} else {
				// 查询系统用户信息
				userService := services.NewUserService()
				sysUser, err := userService.GetSysUserBySerialNumber(serialNumber)

				if err != nil {
					responseMsg = Message{
						Type: "get_sys_user_by_serial_error",
						From: "server",
						To:   c.ID,
						Data: map[string]interface{}{
							"error":         err.Error(),
							"serial_number": serialNumber,
						},
						Timestamp: getCurrentTimestamp(),
					}
				} else {
					responseMsg = Message{
						Type: "get_sys_user_by_serial_success",
						From: "server",
						To:   c.ID,
						Data: map[string]interface{}{
							"sys_user": sysUser,
							"message":  "查询成功",
						},
						Timestamp: getCurrentTimestamp(),
					}
				}
			}
		}
	}

	// 发送响应
	if data, err := json.Marshal(responseMsg); err == nil {
		select {
		case c.Send <- data:
		default:
			close(c.Send)
		}
	}
}

// handleEnterResult 处理百家乐结果录入结算请求
func (c *Client) handleEnterResult(msg Message) {
	var responseMsg Message

	if !c.IsAuthenticated || c.Token == "" {
		responseMsg = Message{
			Type: "enter_result_error",
			From: "server",
			To:   c.ID,
			Data: map[string]interface{}{
				"error": "用户未登录",
			},
			Timestamp: getCurrentTimestamp(),
		}
	} else {
		// 验证令牌
		authService := services.NewAuthService()
		_, err := authService.VerifyToken(c.Token)
		if err != nil {
			responseMsg = Message{
				Type: "enter_result_error",
				From: "server",
				To:   c.ID,
				Data: map[string]interface{}{
					"error": "令牌无效，请重新登录",
				},
				Timestamp: getCurrentTimestamp(),
			}
		} else {
			// 解析请求参数
			var req services.EnterResultRequest
			if data, ok := msg.Data.(map[string]interface{}); ok {
				if v, ok := data["account_period"].(string); ok {
					req.AccountPeriod = v
				}
				if v, ok := data["round_no"].(float64); ok {
					req.RoundNo = int(v)
				}
				if v, ok := data["hand_no"].(float64); ok {
					req.HandNo = int(v)
				}
				if v, ok := data["result"].([]interface{}); ok {
					for _, r := range v {
						if s, ok := r.(string); ok {
							req.Result = append(req.Result, s)
						}
					}
				}
			}

			// 获取客户端IP
			clientIP := c.getClientIP()

			betService := services.NewBetService()
			result, err := betService.EnterResult(req, clientIP)
			if err != nil {
				responseMsg = Message{
					Type: "enter_result_error",
					From: "server",
					To:   c.ID,
					Data: map[string]interface{}{
						"error": err.Error(),
					},
					Timestamp: getCurrentTimestamp(),
				}
			} else {
				responseMsg = Message{
					Type:      "enter_result_success",
					From:      "server",
					To:        c.ID,
					Data:      result,
					Timestamp: getCurrentTimestamp(),
				}
			}
		}
	}

	if data, err := json.Marshal(responseMsg); err == nil {
		select {
		case c.Send <- data:
		default:
			close(c.Send)
		}
	}
}

// handleGetCodeSummary 处理点码查询请求
func (c *Client) handleGetCodeSummary(msg Message) {
	var responseMsg Message

	if !c.IsAuthenticated || c.Token == "" {
		responseMsg = Message{
			Type: "get_code_summary_error",
			From: "server",
			To:   c.ID,
			Data: map[string]interface{}{
				"error": "用户未登录",
			},
			Timestamp: getCurrentTimestamp(),
		}
	} else {
		// 验证令牌
		authService := services.NewAuthService()
		_, err := authService.VerifyToken(c.Token)
		if err != nil {
			responseMsg = Message{
				Type: "get_code_summary_error",
				From: "server",
				To:   c.ID,
				Data: map[string]interface{}{
					"error": "令牌无效，请重新登录",
				},
				Timestamp: getCurrentTimestamp(),
			}
		} else {
			// 解析请求参数
			var req services.GetCodeSummaryRequest
			if data, ok := msg.Data.(map[string]interface{}); ok {
				if v, ok := data["account_period"].(string); ok {
					req.AccountPeriod = v
				}
			}

			// 获取客户端IP
			clientIP := c.getClientIP()

			financeService := services.NewFinanceService()
			result, err := financeService.GetCodeSummary(req, clientIP)
			if err != nil {
				responseMsg = Message{
					Type: "get_code_summary_error",
					From: "server",
					To:   c.ID,
					Data: map[string]interface{}{
						"error": err.Error(),
					},
					Timestamp: getCurrentTimestamp(),
				}
			} else {
				responseMsg = Message{
					Type:      "get_code_summary_success",
					From:      "server",
					To:        c.ID,
					Data:      result,
					Timestamp: getCurrentTimestamp(),
				}
			}
		}
	}

	if data, err := json.Marshal(responseMsg); err == nil {
		select {
		case c.Send <- data:
		default:
			close(c.Send)
		}
	}
}

// handleBatchSettlement 处理批量点码请求
func (c *Client) handleBatchSettlement(msg Message) {
	var responseMsg Message

	if !c.IsAuthenticated || c.Token == "" {
		responseMsg = Message{
			Type: "batch_settlement_error",
			From: "server",
			To:   c.ID,
			Data: map[string]interface{}{
				"error": "用户未登录",
			},
			Timestamp: getCurrentTimestamp(),
		}
	} else {
		// 验证令牌
		authService := services.NewAuthService()
		_, err := authService.VerifyToken(c.Token)
		if err != nil {
			responseMsg = Message{
				Type: "batch_settlement_error",
				From: "server",
				To:   c.ID,
				Data: map[string]interface{}{
					"error": "令牌无效，请重新登录",
				},
				Timestamp: getCurrentTimestamp(),
			}
		} else {
			// 解析批量点码请求
			var req services.BatchSettlementRequest
			if data, ok := msg.Data.(map[string]interface{}); ok {
				if settlementRecords, exists := data["settlement_records"].([]interface{}); exists {
					for _, item := range settlementRecords {
						if recordMap, ok := item.(map[string]interface{}); ok {
							var record services.SettlementRecordRequest
							if v, ok := recordMap["shoe_no"].(float64); ok {
								record.ShoeNo = int(v)
							}
							if v, ok := recordMap["account_period"].(string); ok {
								record.AccountPeriod = v
							}
							if v, ok := recordMap["currency_type"].(float64); ok {
								record.CurrencyType = int8(v)
							}
							if v, ok := recordMap["chips_20w"].(float64); ok {
								record.Chips20W = int(v)
							}
							if v, ok := recordMap["chips_10w"].(float64); ok {
								record.Chips10W = int(v)
							}
							if v, ok := recordMap["chips_5w"].(float64); ok {
								record.Chips5W = int(v)
							}
							if v, ok := recordMap["chips_1w"].(float64); ok {
								record.Chips1W = int(v)
							}
							if v, ok := recordMap["chips_5k"].(float64); ok {
								record.Chips5K = int(v)
							}
							if v, ok := recordMap["chips_1k"].(float64); ok {
								record.Chips1K = int(v)
							}
							if v, ok := recordMap["chips_500"].(float64); ok {
								record.Chips500 = int(v)
							}
							if v, ok := recordMap["chips_100"].(float64); ok {
								record.Chips100 = int(v)
							}
							if v, ok := recordMap["chips_50"].(float64); ok {
								record.Chips50 = int(v)
							}
							if v, ok := recordMap["chips_10"].(float64); ok {
								record.Chips10 = int(v)
							}
							if v, ok := recordMap["chips_5"].(float64); ok {
								record.Chips5 = int(v)
							}
							if v, ok := recordMap["total_amount"].(float64); ok {
								record.TotalAmount = v
							}
							if v, ok := recordMap["client_win_loss"].(float64); ok {
								record.ClientWinLoss = v
							}
							if v, ok := recordMap["amount_tip"].(float64); ok {
								record.AmountTip = v
							}
							if v, ok := recordMap["amount_bottom"].(float64); ok {
								record.AmountBottom = v
							}
							if v, ok := recordMap["wash_rate"].(float64); ok {
								record.WashRate = v
							}
							if v, ok := recordMap["wash_amount"].(float64); ok {
								record.WashAmount = v
							}
							if v, ok := recordMap["wash_tip"].(float64); ok {
								record.WashTip = v
							}
							if v, ok := recordMap["compare_result"].(float64); ok {
								record.CompareResult = v
							}
							if v, ok := recordMap["compare_type"].(float64); ok {
								record.CompareType = int8(v)
							}
							if v, ok := recordMap["remark"].(string); ok {
								record.Remark = v
							}
							if v, ok := recordMap["operator"].(string); ok {
								record.Operator = v
							}
							req.SettlementRecords = append(req.SettlementRecords, record)
						}
					}
				}
			}

			// 获取客户端IP
			clientIP := c.getClientIP()

			financeService := services.NewFinanceService()
			result, err := financeService.BatchSettlement(req, clientIP)
			if err != nil {
				responseMsg = Message{
					Type: "batch_settlement_error",
					From: "server",
					To:   c.ID,
					Data: map[string]interface{}{
						"error": err.Error(),
					},
					Timestamp: getCurrentTimestamp(),
				}
			} else {
				responseMsg = Message{
					Type:      "batch_settlement_success",
					From:      "server",
					To:        c.ID,
					Data:      result,
					Timestamp: getCurrentTimestamp(),
				}
			}
		}
	}

	if data, err := json.Marshal(responseMsg); err == nil {
		select {
		case c.Send <- data:
		default:
			close(c.Send)
		}
	}
}

// handleCloseTable 处理收盘请求
func (c *Client) handleCloseTable(msg Message) {
	var responseMsg Message

	if !c.IsAuthenticated || c.Token == "" {
		responseMsg = Message{
			Type: "close_table_error",
			From: "server",
			To:   c.ID,
			Data: map[string]interface{}{
				"error": "用户未登录",
			},
			Timestamp: getCurrentTimestamp(),
		}
	} else {
		// 验证令牌
		authService := services.NewAuthService()
		_, err := authService.VerifyToken(c.Token)
		if err != nil {
			responseMsg = Message{
				Type: "close_table_error",
				From: "server",
				To:   c.ID,
				Data: map[string]interface{}{
					"error": "令牌无效，请重新登录",
				},
				Timestamp: getCurrentTimestamp(),
			}
		} else {
			// 解析收盘请求参数
			var req services.CloseTableRequest
			// 不再需要解析table_id，将通过IP自动获取

			// 获取客户端IP
			clientIP := c.getClientIP()

			tableService := services.NewTableService()
			result, err := tableService.CloseTable(req, clientIP)
			if err != nil {
				responseMsg = Message{
					Type: "close_table_error",
					From: "server",
					To:   c.ID,
					Data: map[string]interface{}{
						"error": err.Error(),
					},
					Timestamp: getCurrentTimestamp(),
				}
			} else {
				responseMsg = Message{
					Type:      "close_table_success",
					From:      "server",
					To:        c.ID,
					Data:      result,
					Timestamp: getCurrentTimestamp(),
				}
			}
		}
	}

	if data, err := json.Marshal(responseMsg); err == nil {
		select {
		case c.Send <- data:
		default:
			close(c.Send)
		}
	}
}

// handleGetLatestTablesStart 通过客户端IP获取最新桌台开局数据（无需参数）
func (c *Client) handleGetLatestTablesStart() {
	var responseMsg Message

	// 检查用户是否已登录
	if !c.IsAuthenticated || c.Token == "" {
		responseMsg = Message{
			Type: "get_latest_tables_start_error",
			From: "server",
			To:   c.ID,
			Data: map[string]interface{}{
				"error": "用户未登录",
			},
			Timestamp: getCurrentTimestamp(),
		}
	} else {
		// 验证令牌
		authService := services.NewAuthService()
		_, err := authService.VerifyToken(c.Token)
		if err != nil {
			responseMsg = Message{
				Type: "get_latest_tables_start_error",
				From: "server",
				To:   c.ID,
				Data: map[string]interface{}{
					"error": "令牌无效，请重新登录",
				},
				Timestamp: getCurrentTimestamp(),
			}
		} else {
			// 获取客户端IP
			clientIP := c.getClientIP()

			// 执行查询
			tableService := services.NewTableService()
			result, err := tableService.GetLatestTablesStartByIP(clientIP)

			if err != nil {
				responseMsg = Message{
					Type: "get_latest_tables_start_error",
					From: "server",
					To:   c.ID,
					Data: map[string]interface{}{
						"error":     err.Error(),
						"client_ip": clientIP,
					},
					Timestamp: getCurrentTimestamp(),
				}
			} else {
				responseMsg = Message{
					Type: "get_latest_tables_start_success",
					From: "server",
					To:   c.ID,
					Data: map[string]interface{}{
						"message":      result.Message,
						"tables_start": result.TablesStart,
						"client_ip":    clientIP,
					},
					Timestamp: getCurrentTimestamp(),
				}
			}
		}
	}

	// 发送响应
	if data, err := json.Marshal(responseMsg); err == nil {
		select {
		case c.Send <- data:
		default:
			close(c.Send)
		}
	}
}

// handleGetHandRecordsResult 处理获取条口记录结果请求（用于露珠图）
func (c *Client) handleGetHandRecordsResult(msg Message) {
	var responseMsg Message

	// 检查用户是否已登录
	if !c.IsAuthenticated || c.Token == "" {
		responseMsg = Message{
			Type: "get_hand_records_result_error",
			From: "server",
			To:   c.ID,
			Data: map[string]interface{}{
				"error": "用户未登录",
			},
			Timestamp: getCurrentTimestamp(),
		}
	} else {
		// 验证令牌
		authService := services.NewAuthService()
		_, err := authService.VerifyToken(c.Token)
		if err != nil {
			responseMsg = Message{
				Type: "get_hand_records_result_error",
				From: "server",
				To:   c.ID,
				Data: map[string]interface{}{
					"error": "令牌无效，请重新登录",
				},
				Timestamp: getCurrentTimestamp(),
			}
		} else {
			// 解析请求参数
			var req services.GetHandRecordsResultRequest
			if data, ok := msg.Data.(map[string]interface{}); ok {
				if accountPeriod, exists := data["account_period"].(string); exists {
					req.AccountPeriod = accountPeriod
				}
				if shoeNo, exists := data["shoe_no"].(float64); exists {
					req.ShoeNo = int(shoeNo)
				}
			}

			// 获取客户端IP
			clientIP := c.getClientIP()

			// 执行查询
			tableService := services.NewTableService()
			result, err := tableService.GetHandRecordsResult(req, clientIP)

			if err != nil {
				responseMsg = Message{
					Type: "get_hand_records_result_error",
					From: "server",
					To:   c.ID,
					Data: map[string]interface{}{
						"error":     err.Error(),
						"client_ip": clientIP,
					},
					Timestamp: getCurrentTimestamp(),
				}
			} else {
				responseMsg = Message{
					Type: "get_hand_records_result_success",
					From: "server",
					To:   c.ID,
					Data: map[string]interface{}{
						"message":        result.Message,
						"table_id":       result.TableID,
						"table_code":     result.TableCode,
						"account_period": result.AccountPeriod,
						"shoe_no":        result.ShoeNo,
						"records":        result.Records,
						"total_count":    result.TotalCount,
						"client_ip":      clientIP,
					},
					Timestamp: getCurrentTimestamp(),
				}
			}
		}
	}

	// 发送响应
	if data, err := json.Marshal(responseMsg); err == nil {
		select {
		case c.Send <- data:
		default:
			close(c.Send)
		}
	}
}

// handleGetTableStats 处理获取桌台情况统计请求
func (c *Client) handleGetTableStats(msg Message) {
	tableService := services.NewTableService()
	clientIP := c.getClientIP()

	// 解析请求数据
	var req services.GetTableStatsRequest
	if data, ok := msg.Data.(map[string]interface{}); ok {
		if accountPeriod, exists := data["account_period"].(string); exists {
			req.AccountPeriod = accountPeriod
		}
	}

	// 验证必要参数
	if req.AccountPeriod == "" {
		responseMsg := Message{
			Type: "get_table_stats_error",
			From: "server",
			To:   c.ID,
			Data: map[string]interface{}{
				"error": "account_period 参数不能为空",
			},
			Timestamp: getCurrentTimestamp(),
		}
		if data, err := json.Marshal(responseMsg); err == nil {
			select {
			case c.Send <- data:
			default:
				close(c.Send)
			}
		}
		return
	}

	// 获取桌台统计
	stats, err := tableService.GetTableStats(req, clientIP)

	var responseMsg Message
	if err != nil {
		responseMsg = Message{
			Type: "get_table_stats_error",
			From: "server",
			To:   c.ID,
			Data: map[string]interface{}{
				"error": err.Error(),
			},
			Timestamp: getCurrentTimestamp(),
		}
	} else {
		responseMsg = Message{
			Type:      "get_table_stats_success",
			From:      "server",
			To:        c.ID,
			Data:      stats,
			Timestamp: getCurrentTimestamp(),
		}
	}

	// 发送响应
	if data, err := json.Marshal(responseMsg); err == nil {
		select {
		case c.Send <- data:
		default:
			close(c.Send)
		}
	}
}

// handleQueryBetRecords 处理查询下注记录请求
func (c *Client) handleQueryBetRecords(msg Message) {
	var responseMsg Message

	// 检查用户是否已登录
	if !c.IsAuthenticated || c.Token == "" {
		responseMsg = Message{
			Type: "query_bet_records_error",
			From: "server",
			To:   c.ID,
			Data: map[string]interface{}{
				"error": "用户未登录",
			},
			Timestamp: getCurrentTimestamp(),
		}
	} else {
		// 验证令牌
		authService := services.NewAuthService()
		_, err := authService.VerifyToken(c.Token)
		if err != nil {
			responseMsg = Message{
				Type: "query_bet_records_error",
				From: "server",
				To:   c.ID,
				Data: map[string]interface{}{
					"error": "令牌无效，请重新登录",
				},
				Timestamp: getCurrentTimestamp(),
			}
		} else {
			// 解析查询请求数据
			var req services.QueryBetRecordsRequest
			if data, ok := msg.Data.(map[string]interface{}); ok {
				if accountPeriod, exists := data["account_period"].(string); exists {
					req.AccountPeriod = accountPeriod
				}
				if roundNo, exists := data["round_no"].(float64); exists {
					req.RoundNo = int(roundNo)
				}
				if handNo, exists := data["hand_no"].(float64); exists {
					req.HandNo = int(handNo)
				}
			}

			// 获取客户端IP
			clientIP := c.getClientIP()

			// 调用查询服务
			betService := services.NewBetService()
			result, err := betService.QueryBetRecordsFromRedis(req, clientIP)

			if err != nil {
				responseMsg = Message{
					Type: "query_bet_records_error",
					From: "server",
					To:   c.ID,
					Data: map[string]interface{}{
						"error": err.Error(),
					},
					Timestamp: getCurrentTimestamp(),
				}
			} else {
				responseMsg = Message{
					Type:      "query_bet_records_success",
					From:      "server",
					To:        c.ID,
					Data:      result,
					Timestamp: getCurrentTimestamp(),
				}
			}
		}
	}

	// 发送响应
	if data, err := json.Marshal(responseMsg); err == nil {
		select {
		case c.Send <- data:
		default:
			close(c.Send)
		}
	}
}

// handleGetBetLimits 处理获取下注限制请求
func (c *Client) handleGetBetLimits(msg Message) {
	var responseMsg Message

	// 检查用户是否已登录
	if !c.IsAuthenticated || c.Token == "" {
		responseMsg = Message{
			Type: "get_bet_limits_error",
			From: "server",
			To:   c.ID,
			Data: map[string]interface{}{
				"error": "用户未登录",
			},
			Timestamp: getCurrentTimestamp(),
		}
	} else {
		// 验证令牌
		authService := services.NewAuthService()
		_, err := authService.VerifyToken(c.Token)
		if err != nil {
			responseMsg = Message{
				Type: "get_bet_limits_error",
				From: "server",
				To:   c.ID,
				Data: map[string]interface{}{
					"error": "令牌无效，请重新登录",
				},
				Timestamp: getCurrentTimestamp(),
			}
		} else {
			// 解析请求数据
			var req services.GetBetLimitsRequest
			if data, ok := msg.Data.(map[string]interface{}); ok {
				if currencyType, exists := data["currency_type"].(float64); exists {
					req.CurrencyType = int8(currencyType)
				}
			}

			// 验证必要参数
			if req.CurrencyType == 0 {
				responseMsg = Message{
					Type: "get_bet_limits_error",
					From: "server",
					To:   c.ID,
					Data: map[string]interface{}{
						"error": "currency_type 参数不能为空",
					},
					Timestamp: getCurrentTimestamp(),
				}
			} else {
				// 获取客户端IP
				clientIP := c.getClientIP()

				// 调用服务
				betService := services.NewBetService()
				result, err := betService.GetBetLimits(req, clientIP)

				if err != nil {
					responseMsg = Message{
						Type: "get_bet_limits_error",
						From: "server",
						To:   c.ID,
						Data: map[string]interface{}{
							"error": err.Error(),
						},
						Timestamp: getCurrentTimestamp(),
					}
				} else {
					responseMsg = Message{
						Type:      "get_bet_limits_success",
						From:      "server",
						To:        c.ID,
						Data:      result,
						Timestamp: getCurrentTimestamp(),
					}
				}
			}
		}
	}

	// 发送响应
	if data, err := json.Marshal(responseMsg); err == nil {
		select {
		case c.Send <- data:
		default:
			close(c.Send)
		}
	}
}

// handleCheckBetLimits 处理实时检查下注限制请求
func (c *Client) handleCheckBetLimits(msg Message) {
	var responseMsg Message

	// 检查用户是否已登录
	if !c.IsAuthenticated || c.Token == "" {
		responseMsg = Message{
			Type: "check_bet_limits_error",
			From: "server",
			To:   c.ID,
			Data: map[string]interface{}{
				"error": "用户未登录",
			},
			Timestamp: getCurrentTimestamp(),
		}
	} else {
		// 验证令牌
		authService := services.NewAuthService()
		_, err := authService.VerifyToken(c.Token)
		if err != nil {
			responseMsg = Message{
				Type: "check_bet_limits_error",
				From: "server",
				To:   c.ID,
				Data: map[string]interface{}{
					"error": "令牌无效，请重新登录",
				},
				Timestamp: getCurrentTimestamp(),
			}
		} else {
			// 解析请求数据
			var req services.CheckBetLimitsRequest
			if data, ok := msg.Data.(map[string]interface{}); ok {
				if currencyType, exists := data["currency_type"].(float64); exists {
					req.CurrencyType = int8(currencyType)
				}
				if bankerAmount, exists := data["banker_amount"].(float64); exists {
					req.BankerAmount = bankerAmount
				}
				if playerAmount, exists := data["player_amount"].(float64); exists {
					req.PlayerAmount = playerAmount
				}
				if tieAmount, exists := data["tie_amount"].(float64); exists {
					req.TieAmount = tieAmount
				}
				if bankerPairAmount, exists := data["banker_pair_amount"].(float64); exists {
					req.BankerPairAmount = bankerPairAmount
				}
				if playerPairAmount, exists := data["player_pair_amount"].(float64); exists {
					req.PlayerPairAmount = playerPairAmount
				}
				if lucky6Amount, exists := data["lucky_6_amount"].(float64); exists {
					req.Lucky6Amount = lucky6Amount
				}
				if lucky7Amount, exists := data["lucky_7_amount"].(float64); exists {
					req.Lucky7Amount = lucky7Amount
				}
			}

			// 验证必要参数
			if req.CurrencyType == 0 {
				responseMsg = Message{
					Type: "check_bet_limits_error",
					From: "server",
					To:   c.ID,
					Data: map[string]interface{}{
						"error": "currency_type 参数不能为空",
					},
					Timestamp: getCurrentTimestamp(),
				}
			} else {
				// 获取客户端IP
				clientIP := c.getClientIP()

				// 调用服务
				betService := services.NewBetService()
				result, err := betService.CheckBetLimits(req, clientIP)

				if err != nil {
					responseMsg = Message{
						Type: "check_bet_limits_error",
						From: "server",
						To:   c.ID,
						Data: map[string]interface{}{
							"error": err.Error(),
						},
						Timestamp: getCurrentTimestamp(),
					}
				} else {
					responseMsg = Message{
						Type:      "check_bet_limits_success",
						From:      "server",
						To:        c.ID,
						Data:      result,
						Timestamp: getCurrentTimestamp(),
					}
				}
			}
		}
	}

	// 发送响应
	if data, err := json.Marshal(responseMsg); err == nil {
		select {
		case c.Send <- data:
		default:
			close(c.Send)
		}
	}
}

// handleBatchBetEntry 处理批量下注录入请求
func (c *Client) handleBatchBetEntry(msg Message) {
	var responseMsg Message
	startTime := time.Now()

	log.Printf("客户端 %s 开始处理批量下注请求", c.ID)

	// 添加panic恢复机制
	defer func() {
		if r := recover(); r != nil {
			log.Printf("客户端 %s 批量下注录入发生panic: %v", c.ID, r)
			responseMsg = Message{
				Type: "batch_bet_entry_error",
				From: "server",
				To:   c.ID,
				Data: map[string]interface{}{
					"error": fmt.Sprintf("服务器内部错误: %v", r),
				},
				Timestamp: getCurrentTimestamp(),
			}
			if data, err := json.Marshal(responseMsg); err == nil {
				select {
				case c.Send <- data:
					log.Printf("客户端 %s 发送panic错误响应成功", c.ID)
				default:
					log.Printf("客户端 %s 发送panic错误响应失败，客户端可能已断开", c.ID)
				}
			}
		}
		log.Printf("客户端 %s 批量下注处理完成，耗时: %v", c.ID, time.Since(startTime))
	}()

	if !c.IsAuthenticated || c.Token == "" {
		log.Printf("客户端 %s 批量下注失败：用户未登录", c.ID)
		responseMsg = Message{
			Type: "batch_bet_entry_error",
			From: "server",
			To:   c.ID,
			Data: map[string]interface{}{
				"error": "用户未登录",
			},
			Timestamp: getCurrentTimestamp(),
		}
	} else {
		// 验证令牌
		authService := services.NewAuthService()
		_, err := authService.VerifyToken(c.Token)
		if err != nil {
			log.Printf("客户端 %s 批量下注失败：令牌无效", c.ID)
			responseMsg = Message{
				Type: "batch_bet_entry_error",
				From: "server",
				To:   c.ID,
				Data: map[string]interface{}{
					"error": "令牌无效，请重新登录",
				},
				Timestamp: getCurrentTimestamp(),
			}
		} else {
			// 解析批量下注请求
			var req services.BatchBetRequest
			if data, ok := msg.Data.(map[string]interface{}); ok {
				if betList, exists := data["bet_records"].([]interface{}); exists {
					for _, item := range betList {
						if br, ok := item.(map[string]interface{}); ok {
							betRecord := c.mapToBetRecord(br)
							req.BetRecords = append(req.BetRecords, betRecord)
						}
					}
				}
			}

			// 只有在没有错误的情况下才处理
			if responseMsg.Type == "" {
				// 获取客户端IP
				clientIP := c.getClientIP()

				log.Printf("客户端 %s 开始保存批量下注记录", c.ID)
				serviceStartTime := time.Now()

				betService := services.NewBetService()
				result, err := betService.CreateBatchBetRecord(req, clientIP)

				log.Printf("客户端 %s 批量下注记录保存服务调用完成，耗时: %v", c.ID, time.Since(serviceStartTime))

				if err != nil {
					log.Printf("客户端 %s 批量下注记录保存服务调用失败: %v", c.ID, err)
					responseMsg = Message{
						Type: "batch_bet_entry_error",
						From: "server",
						To:   c.ID,
						Data: map[string]interface{}{
							"error": err.Error(),
						},
						Timestamp: getCurrentTimestamp(),
					}
				} else {
					log.Printf("客户端 %s 批量下注记录保存服务调用成功，处理了 %d 条记录", c.ID, result.ProcessedCount)

					responseMsg = Message{
						Type: "batch_bet_entry_success",
						From: "server",
						To:   c.ID,
						Data: map[string]interface{}{
							"success":         result.Success,
							"message":         result.Message,
							"table_id":        result.TableID,
							"processed_count": result.ProcessedCount,
							"results":         result.Results,
						},
						Timestamp: getCurrentTimestamp(),
					}
				}
			}
		}

		// 安全发送响应
		if responseMsg.Type != "" {
			log.Printf("客户端 %s 准备发送响应: %s", c.ID, responseMsg.Type)
			if data, err := json.Marshal(responseMsg); err == nil {
				responseSize := len(data)
				if responseSize > 1024*1024 { // 大于1MB的响应
					log.Printf("客户端 %s 发送大响应: %d bytes", c.ID, responseSize)
				}

				select {
				case c.Send <- data:
					log.Printf("客户端 %s 发送响应成功: %s", c.ID, responseMsg.Type)
				default:
					log.Printf("客户端 %s 发送响应失败，客户端可能已断开", c.ID)
				}
			} else {
				log.Printf("客户端 %s 响应序列化失败: %v", c.ID, err)
			}
		}
	}
}

// mapToBetRecord 将 map 转换为 BetRecordRequest
func (c *Client) mapToBetRecord(br map[string]interface{}) services.BetRecordRequest {
	var bet services.BetRecordRequest
	if v, ok := br["game_type"].(float64); ok {
		bet.GameType = int8(v)
	}
	if v, ok := br["account_period"].(string); ok {
		bet.AccountPeriod = v
	}
	if v, ok := br["round_no"].(float64); ok {
		bet.RoundNo = int(v)
	}
	if v, ok := br["hand_no"].(float64); ok {
		bet.HandNo = int(v)
	}
	if v, ok := br["wash_code"].(string); ok {
		bet.WashCode = v
	}
	if v, ok := br["user_name"].(string); ok {
		bet.UserName = v
	}
	if v, ok := br["currency_type"].(float64); ok {
		bet.CurrencyType = int8(v)
	}
	if v, ok := br["banker_amount"].(float64); ok {
		bet.BankerAmount = v
	}
	if v, ok := br["player_amount"].(float64); ok {
		bet.PlayerAmount = v
	}
	if v, ok := br["tie_amount"].(float64); ok {
		bet.TieAmount = v
	}
	if v, ok := br["banker_pair_amount"].(float64); ok {
		bet.BankerPairAmount = v
	}
	if v, ok := br["player_pair_amount"].(float64); ok {
		bet.PlayerPairAmount = v
	}
	if v, ok := br["lucky_6_amount"].(float64); ok {
		bet.Lucky6Amount = v
	}
	if v, ok := br["lucky_7_amount"].(float64); ok {
		bet.Lucky7Amount = v
	}
	if v, ok := br["win_result"].(string); ok {
		bet.WinResult = v
	}
	if v, ok := br["win_loss"].(float64); ok {
		bet.WinLoss = v
	}
	if v, ok := br["loss"].(float64); ok {
		bet.Loss = v
	}
	if v, ok := br["amount_tip"].(float64); ok {
		bet.AmountTip = v
	}
	if v, ok := br["amount_bottom"].(float64); ok {
		bet.AmountBottom = v
	}
	if v, ok := br["wash_rate"].(float64); ok {
		bet.WashRate = v
	}
	if v, ok := br["wash_amount"].(float64); ok {
		bet.WashAmount = v
	}
	if v, ok := br["wash_tip"].(float64); ok {
		bet.WashTip = v
	}
	return bet
}

// handleGetSettlementDisplay 处理获取收盘展示数据请求
func (c *Client) handleGetSettlementDisplay(msg Message) {
	var responseMsg Message

	// 检查用户是否已登录
	if !c.IsAuthenticated || c.Token == "" {
		responseMsg = Message{
			Type: "get_settlement_display_error",
			From: "server",
			To:   c.ID,
			Data: map[string]interface{}{
				"error": "用户未登录",
			},
			Timestamp: getCurrentTimestamp(),
		}
	} else {
		// 验证令牌
		authService := services.NewAuthService()
		_, err := authService.VerifyToken(c.Token)
		if err != nil {
			responseMsg = Message{
				Type: "get_settlement_display_error",
				From: "server",
				To:   c.ID,
				Data: map[string]interface{}{
					"error": "令牌无效，请重新登录",
				},
				Timestamp: getCurrentTimestamp(),
			}
		} else {
			// 解析请求参数
			var req services.GetSettlementDisplayRequest
			if data, ok := msg.Data.(map[string]interface{}); ok {
				if accountPeriod, exists := data["account_period"].(string); exists {
					req.AccountPeriod = accountPeriod
				}
			}

			// 获取客户端IP
			clientIP := c.getClientIP()

			// 执行查询
			tableService := services.NewTableService()
			result, err := tableService.GetSettlementDisplay(req, clientIP)

			if err != nil {
				responseMsg = Message{
					Type: "get_settlement_display_error",
					From: "server",
					To:   c.ID,
					Data: map[string]interface{}{
						"error":     err.Error(),
						"client_ip": clientIP,
					},
					Timestamp: getCurrentTimestamp(),
				}
			} else {
				responseMsg = Message{
					Type: "get_settlement_display_success",
					From: "server",
					To:   c.ID,
					Data: map[string]interface{}{
						"message": result.Message,
						"data":    result.Data,
					},
					Timestamp: getCurrentTimestamp(),
				}
			}
		}
	}

	// 发送响应消息
	if data, err := json.Marshal(responseMsg); err == nil {
		select {
		case c.Send <- data:
		default:
			close(c.Send)
		}
	}
}

// handleGetBetRecordsList 处理获取下注记录列表请求
func (c *Client) handleGetBetRecordsList(msg Message) {
	// 检查用户是否已登录
	if !c.IsAuthenticated {
		responseMsg := Message{
			Type: "get_bet_records_list_error",
			From: "server",
			To:   c.ID,
			Data: map[string]interface{}{
				"error": "用户未登录，请先登录",
			},
			Timestamp: getCurrentTimestamp(),
		}

		if data, err := json.Marshal(responseMsg); err == nil {
			select {
			case c.Send <- data:
			default:
				close(c.Send)
			}
		}
		return
	}

	// 解析请求参数
	var req services.GetBetRecordsListRequest
	if data, ok := msg.Data.(map[string]interface{}); ok {
		if accountPeriod, exists := data["account_period"].(string); exists {
			req.AccountPeriod = accountPeriod
		}
		if roundNo, exists := data["round_no"].(float64); exists {
			roundNoInt := int(roundNo)
			req.RoundNo = &roundNoInt
		}
		if page, exists := data["page"].(float64); exists {
			req.Page = int(page)
		}
		if pageSize, exists := data["page_size"].(float64); exists {
			req.PageSize = int(pageSize)
		}
	}

	// 获取客户端IP
	clientIP := c.getClientIP()

	// 执行查询
	betService := services.NewBetService()
	result, err := betService.GetBetRecordsList(req, clientIP)

	var responseMsg Message
	if err != nil {
		responseMsg = Message{
			Type: "get_bet_records_list_error",
			From: "server",
			To:   c.ID,
			Data: map[string]interface{}{
				"error":     err.Error(),
				"client_ip": clientIP,
			},
			Timestamp: getCurrentTimestamp(),
		}
	} else {
		responseMsg = Message{
			Type: "get_bet_records_list_success",
			From: "server",
			To:   c.ID,
			Data: map[string]interface{}{
				"message":        result.Message,
				"table_id":       result.TableID,
				"table_code":     result.TableCode,
				"account_period": result.AccountPeriod,
				"round_no":       result.RoundNo,
				"records":        result.Records,
				"total_count":    result.TotalCount,
				"page":           result.Page,
				"page_size":      result.PageSize,
				"total_pages":    result.TotalPages,
				"client_ip":      clientIP,
			},
			Timestamp: getCurrentTimestamp(),
		}
	}

	// 发送响应消息
	if data, err := json.Marshal(responseMsg); err == nil {
		select {
		case c.Send <- data:
		default:
			close(c.Send)
		}
	}
}

// handleGetShuffleRecordsList 处理获取洗牌记录列表请求
func (c *Client) handleGetShuffleRecordsList(msg Message) {
	// 检查用户是否已登录
	if !c.IsAuthenticated {
		responseMsg := Message{
			Type: "get_shuffle_records_list_error",
			From: "server",
			To:   c.ID,
			Data: map[string]interface{}{
				"error": "用户未登录，请先登录",
			},
			Timestamp: getCurrentTimestamp(),
		}

		if data, err := json.Marshal(responseMsg); err == nil {
			select {
			case c.Send <- data:
			default:
				close(c.Send)
			}
		}
		return
	}

	// 无需解析参数，直接使用空的请求结构
	var req services.GetShuffleRecordsListRequest

	// 获取客户端IP
	clientIP := c.getClientIP()

	// 执行查询
	tableService := services.NewTableService()
	result, err := tableService.GetShuffleRecordsList(req, clientIP)

	var responseMsg Message
	if err != nil {
		responseMsg = Message{
			Type: "get_shuffle_records_list_error",
			From: "server",
			To:   c.ID,
			Data: map[string]interface{}{
				"error":     err.Error(),
				"client_ip": clientIP,
			},
			Timestamp: getCurrentTimestamp(),
		}
	} else {
		responseMsg = Message{
			Type: "get_shuffle_records_list_success",
			From: "server",
			To:   c.ID,
			Data: map[string]interface{}{
				"message":     result.Message,
				"table_id":    result.TableID,
				"table_code":  result.TableCode,
				"records":     result.Records,
				"total_count": result.TotalCount,
				"client_ip":   clientIP,
			},
			Timestamp: getCurrentTimestamp(),
		}
	}

	// 发送响应消息
	if data, err := json.Marshal(responseMsg); err == nil {
		select {
		case c.Send <- data:
		default:
			close(c.Send)
		}
	}
}
