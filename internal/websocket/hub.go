package websocket

import (
	"encoding/json"
	"log"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/gorilla/websocket"
)

// Client 表示一个WebSocket客户端连接
type Client struct {
	ID              string          `json:"id"`
	Conn            *websocket.Conn `json:"-"`
	Send            chan []byte     `json:"-"`
	UserID          string          `json:"user_id,omitempty"`
	Username        string          `json:"username,omitempty"`
	Token           string          `json:"-"` // JWT令牌，不在JSON中显示
	IsAuthenticated bool            `json:"is_authenticated"`
}

// Hub 管理所有WebSocket连接
type Hub struct {
	// 已注册的客户端
	Clients map[*Client]bool

	// 注册请求来自客户端
	Register chan *Client

	// 注销请求来自客户端
	Unregister chan *Client

	// 广播消息到所有客户端
	Broadcast chan []byte

	// 客户端ID映射
	ClientMap map[string]*Client

	// 读写锁
	Mutex sync.RWMutex
}

// Message 表示WebSocket消息
type Message struct {
	Type      string      `json:"type"`
	From      string      `json:"from,omitempty"`
	To        string      `json:"to,omitempty"`
	Data      interface{} `json:"data"`
	Timestamp int64       `json:"timestamp"`
}

// NewHub 创建新的Hub实例
func NewHub() *Hub {
	return &Hub{
		Clients:    make(map[*Client]bool),
		Register:   make(chan *Client),
		Unregister: make(chan *Client),
		Broadcast:  make(chan []byte),
		ClientMap:  make(map[string]*Client),
	}
}

// Run 启动Hub
func (h *Hub) Run() {
	for {
		select {
		case client := <-h.Register:
			h.Mutex.Lock()
			h.Clients[client] = true
			h.ClientMap[client.ID] = client
			h.Mutex.Unlock()

			log.Printf("客户端 %s 已连接", client.ID)

			// 发送连接成功消息
			welcomeMsg := Message{
				Type: "connection",
				Data: map[string]string{
					"status":  "connected",
					"message": "WebSocket连接成功",
					"id":      client.ID,
				},
				Timestamp: getCurrentTimestamp(),
			}
			if data, err := json.Marshal(welcomeMsg); err == nil {
				select {
				case client.Send <- data:
				default:
					close(client.Send)
					delete(h.Clients, client)
					delete(h.ClientMap, client.ID)
				}
			}

		case client := <-h.Unregister:
			h.Mutex.Lock()
			if _, ok := h.Clients[client]; ok {
				delete(h.Clients, client)
				delete(h.ClientMap, client.ID)
				close(client.Send)
				log.Printf("客户端 %s 已断开连接", client.ID)
			}
			h.Mutex.Unlock()

		case message := <-h.Broadcast:
			h.Mutex.RLock()
			for client := range h.Clients {
				select {
				case client.Send <- message:
				default:
					close(client.Send)
					delete(h.Clients, client)
					delete(h.ClientMap, client.ID)
				}
			}
			h.Mutex.RUnlock()
		}
	}
}

// BroadcastToAll 广播消息到所有客户端
func (h *Hub) BroadcastToAll(message Message) {
	message.Timestamp = getCurrentTimestamp()
	if data, err := json.Marshal(message); err == nil {
		h.Broadcast <- data
	}
}

// SendToClient 发送消息到指定客户端
func (h *Hub) SendToClient(clientID string, message Message) bool {
	h.Mutex.RLock()
	client, exists := h.ClientMap[clientID]
	h.Mutex.RUnlock()

	if !exists {
		return false
	}

	message.Timestamp = getCurrentTimestamp()
	if data, err := json.Marshal(message); err == nil {
		select {
		case client.Send <- data:
			return true
		default:
			return false
		}
	}
	return false
}

// UpdateClientAuth 更新客户端认证状态
func (h *Hub) UpdateClientAuth(client *Client) {
	h.Mutex.Lock()
	defer h.Mutex.Unlock()

	// 更新客户端映射中的信息
	if _, exists := h.ClientMap[client.ID]; exists {
		h.ClientMap[client.ID] = client
	}

	log.Printf("客户端 %s 认证状态已更新: 用户=%s, 已认证=%v",
		client.ID, client.Username, client.IsAuthenticated)
}

// GetClientCount 获取连接的客户端数量
func (h *Hub) GetClientCount() int {
	h.Mutex.RLock()
	defer h.Mutex.RUnlock()
	return len(h.Clients)
}

// GetClientList 获取所有客户端列表
func (h *Hub) GetClientList() []Client {
	h.Mutex.RLock()
	defer h.Mutex.RUnlock()

	clients := make([]Client, 0, len(h.Clients))
	for client := range h.Clients {
		clients = append(clients, Client{
			ID:              client.ID,
			UserID:          client.UserID,
			Username:        client.Username,
			IsAuthenticated: client.IsAuthenticated,
		})
	}
	return clients
}

// GetAuthenticatedClients 获取已认证的客户端列表
func (h *Hub) GetAuthenticatedClients() []Client {
	h.Mutex.RLock()
	defer h.Mutex.RUnlock()

	clients := make([]Client, 0)
	for client := range h.Clients {
		if client.IsAuthenticated {
			clients = append(clients, Client{
				ID:              client.ID,
				UserID:          client.UserID,
				Username:        client.Username,
				IsAuthenticated: client.IsAuthenticated,
			})
		}
	}
	return clients
}

// GetClientByUserID 根据用户ID获取客户端
func (h *Hub) GetClientByUserID(userID string) *Client {
	h.Mutex.RLock()
	defer h.Mutex.RUnlock()

	for client := range h.Clients {
		if client.UserID == userID && client.IsAuthenticated {
			return client
		}
	}
	return nil
}

// BroadcastToAuthenticatedClients 向所有已认证客户端广播消息
func (h *Hub) BroadcastToAuthenticatedClients(message Message) {
	message.Timestamp = getCurrentTimestamp()
	if data, err := json.Marshal(message); err == nil {
		h.Mutex.RLock()
		for client := range h.Clients {
			if client.IsAuthenticated {
				select {
				case client.Send <- data:
				default:
					close(client.Send)
					delete(h.Clients, client)
					delete(h.ClientMap, client.ID)
				}
			}
		}
		h.Mutex.RUnlock()
	}
}

// NewClient 创建新的客户端
func NewClient(conn *websocket.Conn) *Client {
	return &Client{
		ID:              uuid.New().String(),
		Conn:            conn,
		Send:            make(chan []byte, 256),
		IsAuthenticated: false,
	}
}

// getCurrentTimestamp 获取当前时间戳
func getCurrentTimestamp() int64 {
	return time.Now().Unix()
}
