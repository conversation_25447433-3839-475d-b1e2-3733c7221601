package database

import (
	"context"
	"fmt"
	"time"

	"accounting_enter/internal/config"

	"github.com/go-redis/redis/v8"
)

var RedisClient *redis.Client

func InitRedis() error {
	cfg := config.GlobalConfig.Redis

	RedisClient = redis.NewClient(&redis.Options{
		Addr:         fmt.Sprintf("%s:%d", cfg.Host, cfg.Port),
		Password:     cfg.Password,
		DB:           cfg.Database,
		PoolSize:     cfg.PoolSize,
		MaxRetries:   cfg.MaxRetries,
		DialTimeout:  10 * time.Second,
		ReadTimeout:  10 * time.Second,
		WriteTimeout: 10 * time.Second,
		IdleTimeout:  5 * time.Minute,
		MaxConnAge:   30 * time.Minute,
		// 增加连接池监控
		PoolTimeout:  30 * time.Second,
		MinIdleConns: 5,
	})

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	_, err := RedisClient.Ping(ctx).Result()
	if err != nil {
		return fmt.Errorf("Redis连接测试失败: %v", err)
	}

	// 打印连接池信息
	poolStats := RedisClient.PoolStats()
	fmt.Printf("Redis连接成功 - 连接池状态: 总连接数=%d, 空闲连接数=%d, 使用中连接数=%d\n",
		poolStats.TotalConns, poolStats.IdleConns, poolStats.StaleConns)

	return nil
}

func CloseRedis() error {
	if RedisClient != nil {
		return RedisClient.Close()
	}
	return nil
}
