package routes

import (
	"accounting_enter/docs"
	"accounting_enter/internal/handlers"
	ws "accounting_enter/internal/websocket"

	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

// SetupRoutes 设置路由
func SetupRoutes(hub *ws.Hub) *gin.Engine {
	r := gin.Default()

	// 静态文件服务
	r.Static("/static", "./static")
	r.GET("/", func(c *gin.Context) {
		c.Redirect(302, "/static/index.html")
	})

	// 设置Swagger文档
	docs.SwaggerInfo.BasePath = "/"
	r.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	// WebSocket路由
	r.GET("/ws", handlers.WebSocketHandler(hub))

	// API路由组
	api := r.Group("/api")
	{
		// WebSocket相关API
		wsGroup := api.Group("/ws")
		{
			wsGroup.GET("/stats", handlers.GetWebSocketStats(hub))
			wsGroup.POST("/broadcast", handlers.BroadcastMessage(hub))
		}

		// 健康检查
		api.GET("/health", func(c *gin.Context) {
			c.JSON(200, gin.H{
				"status": "ok",
				"msg":    "服务运行正常",
			})
		})
	}

	return r
}
