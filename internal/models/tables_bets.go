package models

import (
	"time"
)

// TableBet 桌台基础配置
type TableBet struct {
	ID          int       `json:"id" gorm:"primaryKey;autoIncrement;comment:主键ID"`
	TableID     int       `json:"table_id" gorm:"not null;comment:桌台表ID"`
	BetArea     string    `json:"bet_area" gorm:"type:char(8);not null;comment:下注区域(庄,闲等)"`
	Odds        float64   `json:"odds" gorm:"type:decimal(5,4);not null;comment:赔率"`
	MaxBetU     float64   `json:"max_bet_u" gorm:"type:decimal(12,2);default:0.00;comment:U码最大下注金额"`
	MaxBetCash  float64   `json:"max_bet_cash" gorm:"type:decimal(12,2);default:0.00;comment:现金最大下注金额"`
	MaxBetChips float64   `json:"max_bet_chips" gorm:"type:decimal(12,2);default:0.00;comment:筹码最大下注金额"`
	CreateTime  time.Time `json:"create_time" gorm:"default:CURRENT_TIMESTAMP;comment:创建时间"`
}

// TableName 设置表名
func (TableBet) TableName() string {
	return "tables_bets"
}
