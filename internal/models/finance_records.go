package models

import (
	"time"
)

// FinanceRecord 出码/收码记录
type FinanceRecord struct {
	ID            int       `json:"id" gorm:"primaryKey;autoIncrement;comment:主键ID"`
	TableID       int       `json:"table_id" gorm:"not null;comment:桌台ID"`
	TablesName    string    `json:"tables_name" gorm:"type:varchar(32);not null;comment:桌台名称"`
	AccountPeriod string    `json:"account_period" gorm:"type:varchar(100);not null;comment:账期"`
	Type          int8      `json:"type" gorm:"type:tinyint(1);not null;comment:操作类型:1-出码;2-收码;3-加彩;"`
	Status        int8      `json:"status" gorm:"type:tinyint(1);not null;comment:状态:1-申请中;2-同意;3-拒绝;"`
	CurrencyType  int8      `json:"currency_type" gorm:"type:tinyint(1);comment:货币类型:1-筹码;2-现金;3-U码;"`
	TotalAmount   float64   `json:"total_amount" gorm:"type:decimal(12,2);comment:总金额"`
	Operator      string    `json:"operator" gorm:"type:varchar(64);comment:操作人"`
	Approver      string    `json:"approver" gorm:"type:varchar(64);comment:审核人"`
	CreateTime    time.Time `json:"create_time" gorm:"default:CURRENT_TIMESTAMP;comment:创建时间"`
}

// TableName 设置表名
func (FinanceRecord) TableName() string {
	return "finance_records"
}
