package models

import (
	"time"
)

// UserUseLog 客户使用记录表
type UserUseLog struct {
	ID             int64     `json:"id" gorm:"primaryKey;autoIncrement;comment:主键ID"`
	WashCode       string    `json:"wash_code" gorm:"type:varchar(32);not null;uniqueIndex:wash_code;comment:洗码号，用于识别客户身份"`
	Name           string    `json:"name" gorm:"type:varchar(64);comment:客户姓名"`
	UseBeginTime   time.Time `json:"use_begin_time" gorm:"default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;comment:使用开始时间"`
	UseEndTime     time.Time `json:"use_end_time" gorm:"default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;comment:使用结束时间"`
	UpdateUserID   int       `json:"update_user_id" gorm:"not null;comment:修改人编号"`
	UpdateUserName string    `json:"update_user_name" gorm:"type:varchar(32);default:'';comment:修改人名称"`
	CreateTime     time.Time `json:"create_time" gorm:"default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;comment:修改时间"`
}

// TableName 设置表名
func (UserUseLog) TableName() string {
	return "users_use_log"
}
