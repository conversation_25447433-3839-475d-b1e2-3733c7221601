package models

// SysRole 系统角色表
type SysRole struct {
	ID         int    `json:"id" gorm:"primaryKey;autoIncrement;comment:主键ID"`
	Name       string `json:"name" gorm:"type:varchar(150);not null;index:name;comment:角色名称"`
	Code       string `json:"code" gorm:"type:varchar(100) charset utf8mb3;not null;comment:角色标签"`
	Note       string `json:"note" gorm:"type:varchar(255);comment:备注"`
	Sort       int16  `json:"sort" gorm:"type:smallint;default:125;not null;comment:排序"`
	Status     int8   `json:"status" gorm:"type:tinyint(1);default:1;not null;comment:状态：1正常 2停用"`
	CreateUser int    `json:"create_user" gorm:"default:0;not null;comment:添加人"`
	CreateTime uint   `json:"create_time" gorm:"type:int unsigned;default:0;not null;comment:添加时间"`
	UpdateUser int    `json:"update_user" gorm:"default:0;comment:更新人"`
	UpdateTime uint   `json:"update_time" gorm:"type:int unsigned;default:0;comment:更新时间"`
	Mark       int8   `json:"mark" gorm:"type:tinyint(1);default:1;not null;comment:有效标识"`
}

// TableName 设置表名
func (SysRole) TableName() string {
	return "sys_role"
}
