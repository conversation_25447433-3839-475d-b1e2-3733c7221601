package models

import (
	"time"
)

// Table 桌台基础配置
type Table struct {
	ID           int       `json:"id" gorm:"primaryKey;autoIncrement;comment:主键ID"`
	TableCode    string    `json:"table_code" gorm:"type:varchar(32);not null;comment:桌台编号"`
	TablesName   string    `json:"tables_name" gorm:"type:varchar(64);comment:桌台名称"`
	GameType     int8      `json:"game_type" gorm:"type:tinyint(1);comment:游戏类型:1-百家乐;2-龙虎斗;3-百家乐免佣;4-牛牛;5-三公;6-A89;7-庄闲牛;8-骰宝;-轮盘;"`
	TableIP      string    `json:"table_ip" gorm:"type:varchar(20);comment:桌台IP"`
	VideoURL     string    `json:"video_url" gorm:"type:varchar(20);comment:视频地址"`
	Channel      int8      `json:"channel" gorm:"type:tinyint(1);default:1;not null;comment:桌台所属业务通道:1-现场;2-电投;3-网投;"`
	WashUserType string    `json:"wash_user_type" gorm:"type:char(10);default:'';not null;comment:参与洗码用户类型,多选"`
	WashRate     float64   `json:"wash_rate" gorm:"type:decimal(5,4);comment:洗码率"`
	MaxBetU      float64   `json:"max_bet_u" gorm:"type:decimal(12,2);default:0.00;comment:U码最大下注金额"`
	MaxBetCash   float64   `json:"max_bet_cash" gorm:"type:decimal(12,2);default:0.00;comment:现金最大下注金额"`
	MaxBetChips  float64   `json:"max_bet_chips" gorm:"type:decimal(12,2);default:0.00;comment:筹码最大下注金额"`
	TieRate      float64   `json:"tie_rate" gorm:"type:decimal(5,4);not null;comment:龙虎和底返"`
	Status       int8      `json:"status" gorm:"type:tinyint(1);default:1;comment:状态:1-禁用;2-启用;"`
	Memo         string    `json:"memo" gorm:"type:text;comment:备注"`
	CreateTime   time.Time `json:"create_time" gorm:"default:CURRENT_TIMESTAMP;comment:创建时间"`
}

// TableName 设置表名
func (Table) TableName() string {
	return "tables"
}
