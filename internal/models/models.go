// Package models 包含所有数据库模型定义
package models

// 导出所有模型，方便统一使用
var AllModels = []interface{}{
	&BetRecord{},
	&FinanceRecord{},
	&HandRecord{},
	&SettlementRecord{},
	&ShuffleRecord{},
	&SysMenu{},
	&SysRole{},
	&SysRoleMenu{},
	&SysUser{},
	&SysUserRole{},
	&Table{},
	&TableBet{},
	&User{},
	&UserUpdateLog{},
	&UserUseLog{},
	&LoginLogs{},
}

// 常量定义

// 货币类型
const (
	CurrencyTypeChips = 1 // 筹码
	CurrencyTypeCash  = 2 // 现金
	CurrencyTypeUCode = 3 // U码
)

// 用户类型
const (
	UserTypeCompany = 1 // 公司客户
	UserTypeRebateF = 2 // 返点F
	UserTypeRebateW = 3 // 返点W
	UserTypeShare   = 4 // 占成客户
	UserTypeSpecial = 5 // 特殊客户
)

// 账号状态
const (
	UserStatusNormal  = 1 // 正常
	UserStatusFrozen  = 2 // 冻结
	UserStatusDisable = 3 // 禁用
)

// 游戏类型
const (
	GameTypeBaccarat       = 1 // 百家乐
	GameTypeDragonTiger    = 2 // 龙虎斗
	GameTypeBaccaratNoComm = 3 // 百家乐免佣
	GameTypeNiuniu         = 4 // 牛牛
	GameTypeSangong        = 5 // 三公
	GameTypeA89            = 6 // A89
	GameTypeZhuangxianniu  = 7 // 庄闲牛
	GameTypeSicbo          = 8 // 骰宝
	GameTypeRoulette       = 9 // 轮盘
)

// 操作类型（财务记录）
const (
	FinanceTypeOut   = 1 // 出码
	FinanceTypeIn    = 2 // 收码
	FinanceTypeBonus = 3 // 加彩
)

// 手牌状态
const (
	HandStatusSelling     = 1 // 销售中
	HandStatusWaitShuffle = 2 // 等待洗牌
	HandStatusWaitOut     = 3 // 等待出码
)
