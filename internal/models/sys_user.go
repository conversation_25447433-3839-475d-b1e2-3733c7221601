package models

// SysUser 后台用户管理表
type SysUser struct {
	ID           int    `json:"id" gorm:"primaryKey;autoIncrement;comment:主键ID"`
	SerialNumber int    `json:"serial_number" gorm:"type:int(11);default:3;comment:员工编号"`
	Realname     string `json:"realname" gorm:"type:varchar(150);index:realname;comment:真实姓名"`
	Nickname     string `json:"nickname" gorm:"type:varchar(150);comment:昵称"`
	Gender       int8   `json:"gender" gorm:"type:tinyint(1);default:3;comment:性别:1男 2女 3保密"`
	Avatar       string `json:"avatar" gorm:"type:varchar(150);comment:头像"`
	Mobile       string `json:"mobile" gorm:"type:char(11);comment:手机号码"`
	Email        string `json:"email" gorm:"type:varchar(30);comment:邮箱地址"`
	Birthday     uint   `json:"birthday" gorm:"type:int unsigned;default:0;comment:出生日期"`
	DeptID       int    `json:"dept_id" gorm:"default:0;comment:部门ID"`
	LevelID      int    `json:"level_id" gorm:"default:0;comment:职级ID"`
	PositionID   int16  `json:"position_id" gorm:"type:smallint;default:0;comment:岗位ID"`
	ProvinceCode string `json:"province_code" gorm:"type:varchar(50);comment:省份编号"`
	CityCode     string `json:"city_code" gorm:"type:varchar(50);comment:市区编号"`
	DistrictCode string `json:"district_code" gorm:"type:varchar(50);comment:区县编号"`
	Address      string `json:"address" gorm:"type:varchar(255);comment:详细地址"`
	CityName     string `json:"city_name" gorm:"type:varchar(150);comment:所属城市"`
	Username     string `json:"username" gorm:"type:varchar(50);comment:登录用户名"`
	Password     string `json:"password" gorm:"type:varchar(150);comment:登录密码"`
	Salt         string `json:"salt" gorm:"type:varchar(30);comment:盐加密"`
	Intro        string `json:"intro" gorm:"type:varchar(500);comment:个人简介"`
	Status       int8   `json:"status" gorm:"type:tinyint(1);default:1;comment:状态：1正常 2禁用"`
	Note         string `json:"note" gorm:"type:varchar(500);comment:备注"`
	Sort         int    `json:"sort" gorm:"default:125;comment:排序号"`
	LoginNum     int    `json:"login_num" gorm:"default:0;comment:登录次数"`
	LoginIP      string `json:"login_ip" gorm:"type:varchar(20);comment:最近登录IP"`
	LoginTime    uint   `json:"login_time" gorm:"type:int unsigned;default:0;comment:最近登录时间"`
	CreateUser   int    `json:"create_user" gorm:"default:0;comment:添加人"`
	CreateTime   uint   `json:"create_time" gorm:"type:int unsigned;default:0;comment:创建时间"`
	UpdateUser   int    `json:"update_user" gorm:"default:0;comment:更新人"`
	UpdateTime   uint   `json:"update_time" gorm:"type:int unsigned;default:0;comment:更新时间"`
	Mark         int8   `json:"mark" gorm:"type:tinyint(1);default:1;not null;comment:有效标识(1正常 0删除)"`
}

// TableName 设置表名
func (SysUser) TableName() string {
	return "sys_user"
}
