package models

import (
	"time"
)

// User 客户信息表
type User struct {
	ID              int64     `json:"id" gorm:"primaryKey;autoIncrement;comment:主键ID"`
	WashCode        string    `json:"wash_code" gorm:"type:varchar(32);not null;uniqueIndex:wash_code;comment:洗码号，用于识别客户身份"`
	AccountType     int8      `json:"account_type" gorm:"type:tinyint(1);default:1;comment:账号类型:1-主号;2-分线;"`
	MainCode        string    `json:"main_code" gorm:"type:varchar(32);comment:所属主号的洗码号"`
	Name            string    `json:"name" gorm:"type:varchar(64);comment:客户姓名"`
	Phone           string    `json:"phone" gorm:"type:varchar(32);comment:联系电话"`
	Password        string    `json:"password" gorm:"type:varchar(128);comment:登录密码（加密存储）"`
	UserType        int8      `json:"user_type" gorm:"type:tinyint(1);not null;comment:客户类型:1-公司客户;2-返点F;3-返点W;4-占成客户;5-特殊客户;"`
	RebateRate      float64   `json:"rebate_rate" gorm:"type:decimal(5,4);default:0.0000;comment:返点比例，如0.018表示1.8%"`
	IsDragonTiger   int8      `json:"is_dragon_tiger" gorm:"type:tinyint(1);comment:是否龙虎洗码"`
	Status          int8      `json:"status" gorm:"type:tinyint(1);default:1;comment:账号状态:1-正常;2-冻结;3-禁用;"`
	Memo            string    `json:"memo" gorm:"type:text;comment:客户备注信息"`
	OperatorRemark  string    `json:"operator_remark" gorm:"type:text;comment:操作人员备注"`
	CreateTime      time.Time `json:"create_time" gorm:"default:CURRENT_TIMESTAMP;comment:创建时间"`
	AgainCreateTime time.Time `json:"again_create_time" gorm:"default:CURRENT_TIMESTAMP;comment:重开户时间"`
	UpdateTime      time.Time `json:"update_time" gorm:"default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;comment:最后更新时间"`
}

// TableName 设置表名
func (User) TableName() string {
	return "users"
}
