package models

import (
	"time"
)

// UserUpdateLog 客户信息修改记录表
type UserUpdateLog struct {
	ID               int64     `json:"id" gorm:"primaryKey;autoIncrement;comment:主键ID"`
	WashCode         string    `json:"wash_code" gorm:"type:varchar(32);not null;uniqueIndex:wash_code;comment:洗码号"`
	Name             string    `json:"name" gorm:"type:varchar(64);comment:客户姓名"`
	BeforeName       string    `json:"before_name" gorm:"type:varchar(64);comment:客户姓名(修改前)"`
	BeforeClientType int8      `json:"before_client_type" gorm:"type:tinyint(1);not null;comment:客户类型:1-公司客户;2-返点F;3-返点W;4-占成客户;5-特殊客户;"`
	AfterName        string    `json:"after_name" gorm:"type:varchar(64);comment:客户姓名(修改后)"`
	AfterClientType  int8      `json:"after_client_type" gorm:"type:tinyint(1);not null;comment:客户类型:1-公司客户;2-返点F;3-返点W;4-占成客户;5-特殊客户;"`
	UpdateUserID     int       `json:"update_user_id" gorm:"not null;comment:修改人编号"`
	UpdateUserName   string    `json:"update_user_name" gorm:"type:varchar(32);default:'';comment:修改人名称"`
	CreateTime       time.Time `json:"create_time" gorm:"default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;comment:修改时间"`
}

// TableName 设置表名
func (UserUpdateLog) TableName() string {
	return "users_update_log"
}
