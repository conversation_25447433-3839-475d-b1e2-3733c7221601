package models

// SysMenu 系统菜单表
type SysMenu struct {
	ID         int    `json:"id" gorm:"primaryKey;autoIncrement;comment:主键ID"`
	ParentID   int    `json:"parent_id" gorm:"default:0;not null;index:index_pid;comment:父级ID"`
	Title      string `json:"title" gorm:"type:varchar(30);not null;index:index_name;comment:菜单标题"`
	Icon       string `json:"icon" gorm:"type:varchar(50);comment:图标"`
	Path       string `json:"path" gorm:"type:varchar(150);comment:菜单路径"`
	Component  string `json:"component" gorm:"type:varchar(150);comment:菜单组件"`
	Target     string `json:"target" gorm:"type:varchar(30);comment:打开方式：0组件 1内链 2外链"`
	Permission string `json:"permission" gorm:"type:varchar(150);comment:权限标识"`
	Type       int8   `json:"type" gorm:"type:tinyint(1);default:0;not null;comment:类型：0菜单 1节点"`
	Status     int8   `json:"status" gorm:"type:tinyint(1);default:1;comment:状态：1正常 2禁用"`
	Hide       int8   `json:"hide" gorm:"type:tinyint(1);default:1;comment:是否可见：1是 2否"`
	Note       string `json:"note" gorm:"type:varchar(255);comment:备注"`
	Sort       int16  `json:"sort" gorm:"type:smallint;default:125;comment:显示顺序"`
	CreateUser int    `json:"create_user" gorm:"default:0;comment:添加人"`
	CreateTime uint   `json:"create_time" gorm:"type:int unsigned;default:0;comment:创建时间"`
	UpdateUser int    `json:"update_user" gorm:"default:0;comment:更新人"`
	UpdateTime uint   `json:"update_time" gorm:"type:int unsigned;comment:更新时间"`
	Mark       int8   `json:"mark" gorm:"type:tinyint(1);default:1;not null;comment:有效标识"`
}

// TableName 设置表名
func (SysMenu) TableName() string {
	return "sys_menu"
}
