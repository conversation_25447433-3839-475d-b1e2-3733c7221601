package models

import (
	"time"
)

// ShuffleRecord 洗牌记录
type ShuffleRecord struct {
	ID                int64     `json:"id" gorm:"primaryKey;autoIncrement;comment:主键ID"`
	TableID           int64     `json:"table_id" gorm:"not null;comment:桌台ID"`
	TablesName        string    `json:"tables_name" gorm:"type:varchar(32);not null;comment:桌台名称"`
	AccountPeriod     string    `json:"account_period" gorm:"type:varchar(32);default:'';not null;comment:账期"`
	ShoeNo            int       `json:"shoe_no" gorm:"not null;comment:场次编号"`
	CardNo            int       `json:"card_no" gorm:"not null;comment:牌次号"`
	Shift             int8      `json:"shift" gorm:"type:tinyint(1);not null;comment:班次:1-早班;2-晚班;"`
	ShuffleMethod     string    `json:"shuffle_method" gorm:"type:varchar(64);comment:洗牌方式"`
	CardColor         string    `json:"card_color" gorm:"type:varchar(64);comment:牌色"`
	MonitorID         string    `json:"monitor_id" gorm:"type:varchar(64);comment:监场人员ID"`
	MonitorName       string    `json:"monitor_name" gorm:"type:varchar(64);comment:监场人员名称"`
	AdminID           string    `json:"admin_id" gorm:"type:varchar(64);comment:管理员ID"`
	AdminName         string    `json:"admin_name" gorm:"type:varchar(64);comment:管理员名称"`
	ShuffleTablePoker string    `json:"shuffle_table_poker" gorm:"type:varchar(64);comment:洗牌卓牌手"`
	TablePoker        string    `json:"table_poker" gorm:"type:varchar(64);comment:台面洗牌牌手"`
	MonitorPoker      string    `json:"monitor_poker" gorm:"type:varchar(64);comment:监管洗牌手"`
	CutCardDealer     string    `json:"cut_card_dealer" gorm:"type:varchar(64);comment:切牌人"`
	CreateTime        time.Time `json:"create_time" gorm:"default:CURRENT_TIMESTAMP;comment:创建时间"`
}

// TableName 设置表名
func (ShuffleRecord) TableName() string {
	return "shuffle_records"
}
