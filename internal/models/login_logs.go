package models

import (
	"time"
)

// LoginLogs 系统登录日志表
type LoginLogs struct {
	ID          int64     `gorm:"column:id;primaryKey;autoIncrement;comment:主键ID" json:"id"`
	SysUserID   int64     `gorm:"column:sys_user_id;comment:用户ID" json:"sys_user_id"`
	SysUserName string    `gorm:"column:sys_user_name;type:varchar(64);comment:用户名" json:"sys_user_name"`
	IPAddress   string    `gorm:"column:ip_address;type:varchar(64);comment:登录IP地址" json:"ip_address"`
	LoginType   int8      `gorm:"column:login_type;type:tinyint(1);comment:登录类型:1-登录;2-登出;3-交接班" json:"login_type"`
	DeviceInfo  string    `gorm:"column:device_info;type:varchar(128);comment:设备信息或浏览器" json:"device_info"`
	Message     string    `gorm:"column:message;type:varchar(255);comment:登录结果说明" json:"message"`
	LoginTime   time.Time `gorm:"column:login_time;type:datetime;default:CURRENT_TIMESTAMP;comment:登录时间" json:"login_time"`
}

// TableName 指定表名
func (LoginLogs) TableName() string {
	return "login_logs"
}

// 登录类型常量
const (
	LoginTypeLogin    int8 = 1 // 登录
	LoginTypeLogout   int8 = 2 // 登出
	LoginTypeHandover int8 = 3 // 交接班
)

// GetLoginTypeName 获取登录类型名称
func GetLoginTypeName(loginType int8) string {
	switch loginType {
	case LoginTypeLogin:
		return "登录"
	case LoginTypeLogout:
		return "登出"
	case LoginTypeHandover:
		return "交接班"
	default:
		return "未知"
	}
}

// LoginLogResponse 登录日志响应结构
type LoginLogResponse struct {
	ID            int64  `json:"id"`
	SysUserID     int64  `json:"sys_user_id"`
	SysUserName   string `json:"sys_user_name"`
	IPAddress     string `json:"ip_address"`
	LoginType     int8   `json:"login_type"`
	LoginTypeName string `json:"login_type_name"`
	DeviceInfo    string `json:"device_info"`
	Message       string `json:"message"`
	LoginTime     string `json:"login_time"`
}

// ToResponse 转换为响应格式
func (l *LoginLogs) ToResponse() *LoginLogResponse {
	return &LoginLogResponse{
		ID:            l.ID,
		SysUserID:     l.SysUserID,
		SysUserName:   l.SysUserName,
		IPAddress:     l.IPAddress,
		LoginType:     l.LoginType,
		LoginTypeName: GetLoginTypeName(l.LoginType),
		DeviceInfo:    l.DeviceInfo,
		Message:       l.Message,
		LoginTime:     l.LoginTime.Format("2006-01-02 15:04:05"),
	}
}
