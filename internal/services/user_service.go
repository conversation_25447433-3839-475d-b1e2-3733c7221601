package services

import (
	"accounting_enter/internal/database"
	"accounting_enter/internal/models"
	"errors"
	"fmt"

	"gorm.io/gorm"
)

// UserService 用户服务
type UserService struct{}

// NewUserService 创建用户服务实例
func NewUserService() *UserService {
	return &UserService{}
}

// GetUserByWashCodeRequest 通过洗码号获取用户信息的请求结构
type GetUserByWashCodeRequest struct {
	WashCode string `json:"wash_code"`
}

// UserResponse 用户信息响应结构
type UserResponse struct {
	ID              int64   `json:"id"`
	WashCode        string  `json:"wash_code"`
	AccountType     int8    `json:"account_type"`
	AccountTypeName string  `json:"account_type_name"`
	MainCode        string  `json:"main_code"`
	Name            string  `json:"name"`
	Phone           string  `json:"phone"`
	UserType        int8    `json:"user_type"`
	UserTypeName    string  `json:"user_type_name"`
	RebateRate      float64 `json:"rebate_rate"`
	RebateRateText  string  `json:"rebate_rate_text"`
	IsDragonTiger   int8    `json:"is_dragon_tiger"`
	DragonTigerText string  `json:"dragon_tiger_text"`
	Status          int8    `json:"status"`
	StatusName      string  `json:"status_name"`
	Memo            string  `json:"memo"`
	OperatorRemark  string  `json:"operator_remark"`
	CreateTime      string  `json:"create_time"`
	AgainCreateTime string  `json:"again_create_time"`
	UpdateTime      string  `json:"update_time"`
}

// GetUserByWashCode 通过洗码号获取客户信息
func (s *UserService) GetUserByWashCode(washCode string) (*UserResponse, error) {
	// 参数验证
	if washCode == "" {
		return nil, errors.New("洗码号不能为空")
	}

	// 查询用户信息
	var user models.User
	err := database.DB.Where("wash_code = ?", washCode).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("未找到洗码号为 %s 的客户", washCode)
		}
		return nil, fmt.Errorf("查询客户信息失败: %v", err)
	}

	// 检查账号状态，必须是正常状态
	if user.Status != 1 {
		return nil, fmt.Errorf("客户账号状态异常，当前状态: %s", s.getStatusName(user.Status))
	}

	// 格式化响应数据
	response := s.FormatUserResponse(&user)
	return response, nil
}

// FormatUserResponse 格式化用户响应数据
func (s *UserService) FormatUserResponse(user *models.User) *UserResponse {
	return &UserResponse{
		ID:              user.ID,
		WashCode:        user.WashCode,
		AccountType:     user.AccountType,
		AccountTypeName: s.getAccountTypeName(user.AccountType),
		MainCode:        user.MainCode,
		Name:            user.Name,
		Phone:           user.Phone,
		UserType:        user.UserType,
		UserTypeName:    s.getUserTypeName(user.UserType),
		RebateRate:      user.RebateRate,
		RebateRateText:  fmt.Sprintf("%.2f%%", user.RebateRate*100),
		IsDragonTiger:   user.IsDragonTiger,
		DragonTigerText: s.getDragonTigerText(user.IsDragonTiger),
		Status:          user.Status,
		StatusName:      s.getStatusName(user.Status),
		Memo:            user.Memo,
		OperatorRemark:  user.OperatorRemark,
		CreateTime:      user.CreateTime.Format("2006-01-02 15:04:05"),
		AgainCreateTime: user.AgainCreateTime.Format("2006-01-02 15:04:05"),
		UpdateTime:      user.UpdateTime.Format("2006-01-02 15:04:05"),
	}
}

// getAccountTypeName 获取账号类型名称
func (s *UserService) getAccountTypeName(accountType int8) string {
	switch accountType {
	case 1:
		return "主号"
	case 2:
		return "分线"
	default:
		return "未知"
	}
}

// getUserTypeName 获取客户类型名称
func (s *UserService) getUserTypeName(userType int8) string {
	switch userType {
	case 1:
		return "公司客户"
	case 2:
		return "返点F"
	case 3:
		return "返点W"
	case 4:
		return "占成客户"
	case 5:
		return "特殊客户"
	default:
		return "未知"
	}
}

// getDragonTigerText 获取龙虎洗码文本
func (s *UserService) getDragonTigerText(isDragonTiger int8) string {
	switch isDragonTiger {
	case 1:
		return "是"
	case 0:
		return "否"
	default:
		return "未设置"
	}
}

// getStatusName 获取状态名称
func (s *UserService) getStatusName(status int8) string {
	switch status {
	case 1:
		return "正常"
	case 2:
		return "冻结"
	case 3:
		return "禁用"
	default:
		return "未知"
	}
}

// GetUsersByMainCode 根据主号洗码号获取所有分线客户
func (s *UserService) GetUsersByMainCode(mainCode string) ([]*UserResponse, error) {
	// 参数验证
	if mainCode == "" {
		return nil, errors.New("主号洗码号不能为空")
	}

	// 查询分线客户
	var users []models.User
	err := database.DB.Where("main_code = ? AND status = 1", mainCode).Find(&users).Error
	if err != nil {
		return nil, fmt.Errorf("查询分线客户失败: %v", err)
	}

	// 格式化响应数据
	responses := make([]*UserResponse, len(users))
	for i, user := range users {
		responses[i] = s.FormatUserResponse(&user)
	}

	return responses, nil
}

// SearchUsers 搜索客户（支持按姓名、洗码号、电话搜索）
func (s *UserService) SearchUsers(keyword string, limit, offset int) ([]*UserResponse, int64, error) {
	// 参数验证
	if keyword == "" {
		return nil, 0, errors.New("搜索关键词不能为空")
	}

	var users []models.User
	var total int64

	// 构建查询条件
	query := database.DB.Model(&models.User{}).Where("status = 1")

	// 添加搜索条件
	searchCondition := "wash_code LIKE ? OR name LIKE ? OR phone LIKE ?"
	searchValue := "%" + keyword + "%"
	query = query.Where(searchCondition, searchValue, searchValue, searchValue)

	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, fmt.Errorf("查询客户总数失败: %v", err)
	}

	// 获取分页数据
	err = query.Order("create_time DESC").
		Limit(limit).
		Offset(offset).
		Find(&users).Error
	if err != nil {
		return nil, 0, fmt.Errorf("搜索客户失败: %v", err)
	}

	// 格式化响应数据
	responses := make([]*UserResponse, len(users))
	for i, user := range users {
		responses[i] = s.FormatUserResponse(&user)
	}

	return responses, total, nil
}

// GetSysUserBySerialNumberRequest 通过员工编号获取系统用户信息的请求结构
type GetSysUserBySerialNumberRequest struct {
	SerialNumber string `json:"serial_number"`
}

// SysUserSimpleResponse 系统用户简单信息响应结构
type SysUserSimpleResponse struct {
	ID           int    `json:"id"`            // 主键编号
	SerialNumber int    `json:"serial_number"` // 员工编号
	Realname     string `json:"realname"`      // 真实姓名
}

// GetSysUserBySerialNumber 通过员工编号获取系统用户信息
func (s *UserService) GetSysUserBySerialNumber(serialNumber string) (*SysUserSimpleResponse, error) {
	// 参数验证
	if serialNumber == "" {
		return nil, errors.New("员工编号必传")
	}

	// 查询系统用户信息
	var sysUser models.SysUser
	err := database.DB.Where("serial_number = ? AND status = 1 AND mark = 1", serialNumber).First(&sysUser).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("未找到员工编号为 %s 的正常状态用户", serialNumber)
		}
		return nil, fmt.Errorf("查询系统用户信息失败: %v", err)
	}

	// 构造响应数据
	response := &SysUserSimpleResponse{
		ID:           sysUser.ID,
		SerialNumber: sysUser.SerialNumber,
		Realname:     sysUser.Realname,
	}

	return response, nil
}
