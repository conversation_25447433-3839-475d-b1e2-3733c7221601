package services

import (
	"accounting_enter/internal/database"
	"accounting_enter/internal/models"
	"crypto/md5"
	"crypto/rand"
	"encoding/hex"
	"errors"
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"gorm.io/gorm"
)

// AuthService 认证服务
type AuthService struct{}

// NewAuthService 创建认证服务实例
func NewAuthService() *AuthService {
	return &AuthService{}
}

// JWT密钥，实际项目中应该从配置文件读取
var jwtSecret = []byte("accounting_enter_jwt_secret_key_2024")

// Claims JWT声明结构
type Claims struct {
	UserID   int    `json:"user_id"`
	Username string `json:"username"`
	Realname string `json:"realname"`
	jwt.RegisteredClaims
}

// LoginRequest 登录请求结构
type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// LoginResponse 登录响应结构
type LoginResponse struct {
	Token    string    `json:"token"`
	UserInfo *UserInfo `json:"user_info"`
}

// UserInfo 用户信息结构
type UserInfo struct {
	ID         int    `json:"id"`
	Username   string `json:"username"`
	Realname   string `json:"realname"`
	Nickname   string `json:"nickname"`
	Gender     int8   `json:"gender"`
	GenderName string `json:"gender_name"`
	Avatar     string `json:"avatar"`
	Mobile     string `json:"mobile"`
	Email      string `json:"email"`
	DeptID     int    `json:"dept_id"`
	Status     int8   `json:"status"`
	StatusName string `json:"status_name"`
	LoginTime  uint   `json:"login_time"`
	LoginIP    string `json:"login_ip"`
	CreateTime uint   `json:"create_time"`
}

// Login 用户登录
func (s *AuthService) Login(req LoginRequest, clientIP string) (*LoginResponse, error) {
	// 参数验证
	if req.Username == "" {
		return nil, errors.New("用户名不能为空")
	}
	if req.Password == "" {
		return nil, errors.New("密码不能为空")
	}

	// 查询用户
	var user models.SysUser
	err := database.DB.Where("username = ? AND mark = 1", req.Username).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 记录登录失败日志（已移除登录日志功能）
			// 可在此处添加其他日志记录方式
			return nil, errors.New("用户名或密码错误")
		}
		return nil, fmt.Errorf("查询用户失败: %v", err)
	}

	// 检查用户状态
	if user.Status != 1 {
		// 记录登录失败日志（已移除登录日志功能）
		// 可在此处添加其他日志记录方式
		return nil, errors.New("账号已被禁用，请联系管理员")
	}

	// 验证密码
	if !s.verifyPassword(req.Password, user.Password, user.Salt) {
		// 记录登录失败日志（已移除登录日志功能）
		// 可在此处添加其他日志记录方式
		return nil, errors.New("用户名或密码错误")
	}

	// 更新登录信息：累加登录次数，记录IP和时间
	now := uint(time.Now().Unix())
	err = database.DB.Model(&user).Updates(map[string]interface{}{
		"login_num":  gorm.Expr("login_num + 1"), // 累加登录次数
		"login_ip":   clientIP,                   // 记录登录IP
		"login_time": now,                        // 记录登录时间
	}).Error
	if err != nil {
		// 登录信息更新失败不影响登录流程，只记录日志
		fmt.Printf("更新用户登录信息失败: %v\n", err)
	}

	// 生成JWT令牌
	token, err := s.generateToken(&user)
	if err != nil {
		// 记录登录失败日志（已移除登录日志功能）
		// 可在此处添加其他日志记录方式
		return nil, fmt.Errorf("生成令牌失败: %v", err)
	}

	// 记录登录成功日志（已移除登录日志功能）
	// 可在此处添加其他日志记录方式

	// 构造响应
	response := &LoginResponse{
		Token:    token,
		UserInfo: s.FormatUserInfo(&user, clientIP, now),
	}

	return response, nil
}

// VerifyToken 验证JWT令牌
func (s *AuthService) VerifyToken(tokenString string) (*Claims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		return jwtSecret, nil
	})

	if err != nil {
		return nil, fmt.Errorf("令牌解析失败: %v", err)
	}

	if claims, ok := token.Claims.(*Claims); ok && token.Valid {
		return claims, nil
	}

	return nil, errors.New("无效的令牌")
}

// GetUserByID 根据ID获取用户信息
func (s *AuthService) GetUserByID(userID int) (*models.SysUser, error) {
	var user models.SysUser
	err := database.DB.Where("id = ? AND mark = 1", userID).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("用户不存在")
		}
		return nil, fmt.Errorf("查询用户失败: %v", err)
	}
	return &user, nil
}

// ChangePassword 修改密码
func (s *AuthService) ChangePassword(userID int, oldPassword, newPassword string) error {
	// 参数验证
	if oldPassword == "" {
		return errors.New("原密码不能为空")
	}
	if newPassword == "" {
		return errors.New("新密码不能为空")
	}
	if len(newPassword) < 6 {
		return errors.New("新密码长度不能少于6位")
	}

	// 获取用户信息
	user, err := s.GetUserByID(userID)
	if err != nil {
		return err
	}

	// 验证原密码
	if !s.verifyPassword(oldPassword, user.Password, user.Salt) {
		return errors.New("原密码错误")
	}

	// 生成新的盐值和密码哈希
	salt := s.generateSalt()
	hashedPassword := s.hashPassword(newPassword, salt)

	// 更新密码
	err = database.DB.Model(user).Updates(map[string]interface{}{
		"password":    hashedPassword,
		"salt":        salt,
		"update_time": uint(time.Now().Unix()),
	}).Error
	if err != nil {
		return fmt.Errorf("更新密码失败: %v", err)
	}

	return nil
}

// generateToken 生成JWT令牌
func (s *AuthService) generateToken(user *models.SysUser) (string, error) {
	// 设置令牌过期时间（24小时）
	expirationTime := time.Now().Add(24 * time.Hour)

	// 创建声明
	claims := &Claims{
		UserID:   user.ID,
		Username: user.Username,
		Realname: user.Realname,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expirationTime),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "accounting_enter",
			Subject:   fmt.Sprintf("%d", user.ID),
		},
	}

	// 创建令牌
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// 签名令牌
	tokenString, err := token.SignedString(jwtSecret)
	if err != nil {
		return "", err
	}

	return tokenString, nil
}

// hashPassword 哈希密码
func (s *AuthService) hashPassword(password, salt string) string {
	// 使用MD5哈希（实际项目中建议使用bcrypt）
	hash := md5.Sum([]byte(password + salt))
	return hex.EncodeToString(hash[:])
}

// verifyPassword 验证密码
func (s *AuthService) verifyPassword(password, hashedPassword, salt string) bool {
	return s.hashPassword(password, salt) == hashedPassword
}

// generateSalt 生成随机盐值
func (s *AuthService) generateSalt() string {
	bytes := make([]byte, 16)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}

// formatUserInfo 格式化用户信息
func (s *AuthService) FormatUserInfo(user *models.SysUser, loginIP string, loginTime uint) *UserInfo {
	return &UserInfo{
		ID:         user.ID,
		Username:   user.Username,
		Realname:   user.Realname,
		Nickname:   user.Nickname,
		Gender:     user.Gender,
		GenderName: s.getGenderName(user.Gender),
		Avatar:     user.Avatar,
		Mobile:     user.Mobile,
		Email:      user.Email,
		DeptID:     user.DeptID,
		Status:     user.Status,
		StatusName: s.getStatusName(user.Status),
		LoginTime:  loginTime,
		LoginIP:    loginIP,
		CreateTime: user.CreateTime,
	}
}

// getGenderName 获取性别名称
func (s *AuthService) getGenderName(gender int8) string {
	switch gender {
	case 1:
		return "男"
	case 2:
		return "女"
	case 3:
		return "保密"
	default:
		return "未知"
	}
}

// getStatusName 获取状态名称
func (s *AuthService) getStatusName(status int8) string {
	switch status {
	case 1:
		return "正常"
	case 2:
		return "禁用"
	default:
		return "未知"
	}
}

// CreateUser 创建用户（管理员功能）
func (s *AuthService) CreateUser(req CreateUserRequest) error {
	// 参数验证
	if req.Username == "" {
		return errors.New("用户名不能为空")
	}
	if req.Password == "" {
		return errors.New("密码不能为空")
	}
	if len(req.Password) < 6 {
		return errors.New("密码长度不能少于6位")
	}

	// 检查用户名是否已存在
	var count int64
	err := database.DB.Model(&models.SysUser{}).Where("username = ? AND mark = 1", req.Username).Count(&count).Error
	if err != nil {
		return fmt.Errorf("检查用户名失败: %v", err)
	}
	if count > 0 {
		return errors.New("用户名已存在")
	}

	// 生成盐值和密码哈希
	salt := s.generateSalt()
	hashedPassword := s.hashPassword(req.Password, salt)

	// 创建用户
	now := uint(time.Now().Unix())
	user := models.SysUser{
		Username:   req.Username,
		Password:   hashedPassword,
		Salt:       salt,
		Realname:   req.Realname,
		Nickname:   req.Nickname,
		Gender:     req.Gender,
		Mobile:     req.Mobile,
		Email:      req.Email,
		Status:     1, // 默认启用
		CreateTime: now,
		UpdateTime: now,
		Mark:       1,
	}

	err = database.DB.Create(&user).Error
	if err != nil {
		return fmt.Errorf("创建用户失败: %v", err)
	}

	return nil
}

// CreateUserRequest 创建用户请求结构
type CreateUserRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
	Realname string `json:"realname"`
	Nickname string `json:"nickname"`
	Gender   int8   `json:"gender"`
	Mobile   string `json:"mobile"`
	Email    string `json:"email"`
}

// Logout 用户登出（新增方法）
func (s *AuthService) Logout(userID int, username, clientIP string) error {
	// 记录登出日志（已移除登录日志功能）
	// 可在此处添加其他日志记录方式

	return nil
}
