# 下注记录列表接口说明

## 接口概述

新建 `get_bet_records_list` 接口，支持列表查询，前端可以通过场次编号查询，默认获取当前桌台的数据，支持分页功能。

## 接口详情

### 消息类型
`get_bet_records_list`

### 功能特性
1. **自动桌台识别**：根据客户端IP自动获取对应桌台信息
2. **场次过滤**：支持按场次编号过滤查询
3. **分页查询**：支持分页，提高查询效率
4. **数据完整性**：返回完整的下注记录信息

### 请求参数

| 参数名 | 类型 | 必需 | 说明 |
|--------|------|------|------|
| account_period | string | 是 | 账期，格式YYYYMMDD |
| round_no | int | 否 | 场次编号，不传则查询所有场次 |
| page | int | 否 | 页码，默认1 |
| page_size | int | 否 | 每页数量，默认20，最大100 |

### 响应数据

#### 成功响应字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| message | string | 响应消息 |
| table_id | int | 桌台ID |
| table_code | string | 桌台编号 |
| account_period | string | 账期 |
| round_no | int | 场次编号（如果指定） |
| records | array | 下注记录数组 |
| total_count | int | 总记录数 |
| page | int | 当前页码 |
| page_size | int | 每页数量 |
| total_pages | int | 总页数 |

#### 下注记录字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int | 记录ID |
| table_id | int | 桌台ID |
| account_period | string | 账期 |
| round_no | int | 场次编号 |
| hand_no | int | 局号 |
| wash_code | string | 洗码号 |
| user_name | string | 客户姓名 |
| currency_type | int | 货币类型 |
| currency_type_name | string | 货币类型名称 |
| banker_amount | float | 庄下注金额 |
| player_amount | float | 闲下注金额 |
| tie_amount | float | 和下注金额 |
| banker_pair_amount | float | 庄对下注金额 |
| player_pair_amount | float | 闲对下注金额 |
| lucky_6_amount | float | 幸运6下注金额 |
| lucky_7_amount | float | 幸运7下注金额 |
| total_bet_amount | float | 总下注金额 |
| win_result | string | 开奖结果 |
| win_loss | float | 输赢金额 |
| loss | float | 输口 |
| amount_tip | float | 小费 |
| amount_bottom | float | 和底 |
| wash_rate | float | 洗码率 |
| wash_amount | float | 洗码量 |
| wash_tip | float | 洗码费 |
| create_time | string | 创建时间 |

## 实现的文件

### 1. 服务层
- `internal/services/bet_service.go`
  - 新增 `GetBetRecordsListRequest` 结构体
  - 新增 `GetBetRecordsListResponse` 结构体
  - 新增 `GetBetRecordsList` 方法

### 2. WebSocket处理
- `internal/websocket/client.go`
  - 新增 `get_bet_records_list` case处理
  - 新增 `handleGetBetRecordsList` 方法

### 3. 文档
- `docs/websocket_api.md` - 新增接口文档

### 4. 测试页面
- `static/test_bet_records_list.html` - 专用测试页面
- `static/api_list.html` - 添加测试页面链接

## 核心功能

### 1. 智能桌台识别
```go
// 通过IP获取桌台信息
tableService := NewTableService()
table, err := tableService.GetTableByIP(clientIP)
```

### 2. 灵活查询条件
```go
// 构建查询条件
query := database.DB.Where("table_id = ? AND account_period = ?", table.ID, req.AccountPeriod)

// 如果指定了场次编号，添加场次过滤
if req.RoundNo != nil {
    query = query.Where("round_no = ?", *req.RoundNo)
}
```

### 3. 分页处理
```go
// 计算分页
offset := (req.Page - 1) * req.PageSize
totalPages := int((totalCount + int64(req.PageSize) - 1) / int64(req.PageSize))

// 查询分页数据
err = query.Order("round_no DESC, hand_no DESC, id DESC").
    Offset(offset).
    Limit(req.PageSize).
    Find(&betRecords).Error
```

### 4. 数据格式化
```go
// 计算总下注金额
totalBetAmount := record.BankerAmount + record.PlayerAmount + record.TieAmount +
    record.BankerPairAmount + record.PlayerPairAmount + record.Lucky6Amount + record.Lucky7Amount

// 格式化货币类型名称
CurrencyTypeName: s.getCurrencyTypeName(record.CurrencyType)
```

## 使用示例

### JavaScript 客户端
```javascript
// 查询指定场次的下注记录
const message = {
    type: 'get_bet_records_list',
    data: {
        account_period: '********',
        round_no: 1,
        page: 1,
        page_size: 20
    }
};

ws.send(JSON.stringify(message));
```

### 响应处理
```javascript
ws.onmessage = function(event) {
    const response = JSON.parse(event.data);
    
    if (response.type === 'get_bet_records_list_success') {
        const data = response.data;
        console.log(`查询成功，共 ${data.total_count} 条记录`);
        console.log(`当前第 ${data.page} 页，共 ${data.total_pages} 页`);
        
        // 处理记录数据
        data.records.forEach(record => {
            console.log(`${record.wash_code}: ${record.total_bet_amount}`);
        });
    }
};
```

## 测试页面功能

### 1. 连接管理
- WebSocket连接/断开
- 用户登录验证

### 2. 查询功能
- 账期输入
- 场次编号过滤（可选）
- 分页参数设置

### 3. 结果展示
- 汇总信息显示
- 详细记录表格
- 分页导航
- 货币类型标识

### 4. 交互特性
- 实时日志显示
- 分页跳转
- 数据格式化显示

## 性能优化

### 1. 分页限制
- 默认每页20条记录
- 最大每页100条记录
- 防止大量数据查询

### 2. 索引优化
- 按桌台ID和账期查询
- 支持场次编号过滤
- 按时间倒序排列

### 3. 数据缓存
- 桌台信息Redis缓存
- 减少数据库查询

## 总结

新的下注记录列表接口提供了完整的查询功能，支持：
- 自动桌台识别
- 灵活的场次过滤
- 高效的分页查询
- 完整的数据展示
- 友好的测试界面

接口设计考虑了性能和用户体验，为前端提供了强大的数据查询能力。
