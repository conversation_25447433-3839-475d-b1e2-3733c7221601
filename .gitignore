# 编译输出
*.exe
*.exe~
*.dll
*.so
*.dylib
accounting_enter
accounting_enter_unix

# 测试二进制文件
*.test

# 输出覆盖率文件
*.out

# Go工作空间文件
go.work

# 依赖目录
vendor/

# 日志文件
logs/
*.log

# 配置文件（敏感信息）
config/config.prod.yaml
config/local.yaml

# IDE文件
.vscode/
.idea/
*.swp
*.swo
*~

# OS生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 临时文件
*.tmp
*.temp

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 缓存文件
*.cache

# Docker相关
.dockerignore
Dockerfile 