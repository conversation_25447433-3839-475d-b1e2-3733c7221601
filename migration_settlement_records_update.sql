-- 更新settlement_records表结构
-- 修改compare_result字段类型并新增compare_type字段

-- 1. 修改compare_result字段类型从varchar(255)改为decimal(12,6)
-- 注意：这会清空现有数据，因为类型不兼容
ALTER TABLE settlement_records 
MODIFY COLUMN compare_result decimal(12, 6) NULL COMMENT '点码与注单比对结果';

-- 2. 新增compare_type字段
ALTER TABLE settlement_records 
ADD COLUMN compare_type tinyint(1) NULL COMMENT '比较类型:1-多收;2-多出;3-正确;' 
AFTER compare_result;

-- 3. 更新现有数据的默认值（如果有数据的话）
-- 将compare_result设为0，compare_type设为0（表示暂无数据）
UPDATE settlement_records 
SET compare_result = 0.00, compare_type = 0 
WHERE compare_result IS NULL OR compare_type IS NULL;

-- 4. 验证表结构
DESCRIBE settlement_records;

-- 5. 显示更新后的表结构信息
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'settlement_records' 
  AND COLUMN_NAME IN ('compare_result', 'compare_type')
ORDER BY ORDINAL_POSITION;
